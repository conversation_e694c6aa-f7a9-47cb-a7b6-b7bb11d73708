plugins {
    kotlin("jvm") version "2.2.0"
}

group = "org.example"
version = "1.0-SNAPSHOT"

repositories {
    mavenCentral()
}

dependencies {
    // Kotlin 基础依赖
    implementation(kotlin("stdlib"))
    implementation(kotlin("reflect"))

    // 协程支持
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.0")

    // ktor
    implementation("io.ktor:ktor-server-netty:3.0.2")
    implementation("io.ktor:ktor-server-core:3.0.2")
    implementation("io.ktor:ktor-serialization-jackson:3.0.2")
    implementation("io.ktor:ktor-server-html-builder:3.0.2")
//    implementation("io.ktor:ktor-server-freemarker:2.3.7")

    // 数据库与ORM
    implementation("org.jetbrains.exposed:exposed-core:0.45.0")
    implementation("org.jetbrains.exposed:exposed-dao:0.45.0")
    implementation("org.jetbrains.exposed:exposed-jdbc:0.45.0")
    implementation("org.jetbrains.exposed:exposed-java-time:0.45.0")
//    implementation("com.clickhouse:clickhouse-jdbc:0.9.1:shaded-all")
    implementation("com.zaxxer:HikariCP:5.1.0")
    implementation("org.mariadb.jdbc:mariadb-java-client:3.3.3")

    // 日志系统
    implementation("ch.qos.logback:logback-classic:1.5.13")
    implementation("org.slf4j:slf4j-api:2.0.13")
    implementation("io.ktor:ktor-server-content-negotiation:3.0.2")
    implementation("io.ktor:ktor-server-content-negotiation:3.0.2")
    implementation("io.ktor:ktor-server-freemarker:2.3.7")

    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.15.2")

    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.9.6")

    testImplementation(kotlin("test"))
}

tasks.test {
    useJUnitPlatform()
}
kotlin {
    jvmToolchain(17)
}