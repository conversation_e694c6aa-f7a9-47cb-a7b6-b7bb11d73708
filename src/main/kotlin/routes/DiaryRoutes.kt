package org.example.routes

import io.ktor.http.HttpStatusCode
import io.ktor.server.freemarker.FreeMarkerContent
import io.ktor.server.html.respondHtml
import io.ktor.server.request.receiveParameters
import io.ktor.server.response.respond
import io.ktor.server.response.respondRedirect
import io.ktor.server.response.respondText
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import kotlinx.html.FormMethod
import kotlinx.html.a
import kotlinx.html.head
import kotlinx.html.body
import kotlinx.html.div
import kotlinx.html.form
import kotlinx.html.h1
import kotlinx.html.header
import kotlinx.html.i
import kotlinx.html.li
import kotlinx.html.nav
import kotlinx.html.p
import kotlinx.html.script
import kotlinx.html.span
import kotlinx.html.submitInput
import kotlinx.html.textInput
import kotlinx.html.title
import kotlinx.html.ul
import kotlinx.html.unsafe
import org.example.table.Diary

fun Route.diaryRoutes() {
    get("/") {
        val entries = Diary.all()
        call.respondHtml {
            head {
                title { +"My Diary" }
            }
            body("bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100") {
                div("max-w-2xl mx-auto mt-10 p-6 bg-gray-100 rounded") {
                    h1("text-2xl font-bold mb-4") { +"📔 My Diary" }

                    a(href = "/new", classes = "text-blue-600 hover:underline") { +"✍️ Write new entry" }

                    ul("mt-6 space-y-4") {
                        entries.forEach {
                            li("bg-white p-4 shadow rounded") {
                                a(href = "/entry/${it.id}", classes = "text-lg text-blue-700 hover:text-blue-500") {
                                    +it.title
                                }
                                p("text-sm text-gray-500") { +it.createdAt }
                            }
                        }
                    }
                }
            }
        }
    }

    get("/new") {
        call.respondHtml {
            body {
                h1 { +"Write a New Entry" }
                form(action = "/submit", method = FormMethod.post) {
                    p {
                        +"Title:"
                        textInput(name = "title") { required = true }
                    }
                    p {
                        +"Content:"
                    }
                    p {
                        submitInput { value = "Save" }
                    }
                }
            }
        }
    }

    post("/submit") {
        val params = call.receiveParameters()
        val title = params["title"] ?: "Untitled"
        val content = params["content"] ?: ""
        Diary.add(title, content)
        call.respondRedirect("/")
    }

    get("/entry/{id}") {
        val id = call.parameters["id"]?.toIntOrNull()
        if (id != null) {
            val entry = Diary.get(id)
            if (entry != null) {
                call.respond(FreeMarkerContent("entry.ftl", mapOf("entry" to entry)))
            } else {
                call.respondText("Not found", status = HttpStatusCode.NotFound)
            }
        } else {
            call.respondText("Invalid ID", status = HttpStatusCode.BadRequest)
        }
    }
}