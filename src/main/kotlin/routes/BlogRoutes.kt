package org.example.routes

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.KotlinModule
import io.ktor.http.Parameters
import io.ktor.server.html.respondHtml
import io.ktor.server.request.receiveParameters
import io.ktor.server.response.respondRedirect
import io.ktor.server.response.respondText
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import kotlinx.html.*
import org.example.model.BlogEntry
import org.example.model.ContentEntry
import org.example.model.ContentType
import org.example.model.SectionEntry
import org.example.model.buildContent
import org.example.model.testBlogEntry
import org.example.table.BlogService
import org.example.util.BlogUtil.getContentByParams
import org.example.util.localDateTimeFormatter
import org.example.util.objectMapper
import org.example.uuid
import java.text.SimpleDateFormat
import java.time.LocalDateTime

fun Route.blogRoutes(blogService: BlogService) {
    get("/") {
        val blogs = blogService.getAllBlogs()
        call.respondHtml {
            head {
                title { +"My Diary" }
                meta(charset = "utf-8")
                meta(name = "viewport", content = "width=device-width, initial-scale=1.0")

                // Tailwind
                script { src = "https://cdn.tailwindcss.com" }
                script {
                    unsafe {
                        +"""
                    tailwind.config = {
                        theme: {
                            extend: {
                                colors: {
                                    warm: '#FAF3E0'
                                }
                            }
                        }
                    }
                    """.trimIndent()
                    }
                }

                // 白色背景 + 网格
                style {
                    unsafe {
                        +"""
                    body {
                        background-color: white;
                        background-image:
                            linear-gradient(to right, rgba(0,0,0,0.03) 1px, transparent 1px),
                            linear-gradient(to bottom, rgba(0,0,0,0.03) 1px, transparent 1px);
                        background-size: 24px 24px;
                    }
                    """.trimIndent()
                    }
                }

                // Lucide
                script { src = "https://unpkg.com/lucide@latest" }
                script {
                    unsafe {
                        +"""
                    document.addEventListener("DOMContentLoaded", () => {
                        lucide.createIcons();
                    });
                    """.trimIndent()
                    }
                }
            }
            body("text-gray-900 font-serif leading-relaxed") {
                // 外层容器
                div("max-w-6xl mx-auto px-4 py-12 mt-12") {
                    h1("text-4xl font-bold mb-8 text-center") { +"My Diary" }

                    // 主体内容
                    main("py-8 lg:flex lg:gap-8 w-full") {
                        // 侧边目录
                        aside("lg:w-1/5 mb-8 lg:mb-0") {
                            div("sticky top-8") {
                                div("p-4 border border-gray-200 dark:border-gray-800 rounded-lg bg-gray-50/80 dark:bg-gray-800") {
                                    nav("toc text-sm") {
                                        ul("space-y-2") {
                                            li {
                                                a(href = "/", classes = "flex items-center text-gray-700 hover:text-gray-900") {
                                                    i("w-4 h-4 mr-2") { attributes["data-lucide"] = "home" }
                                                    +"首页"
                                                }
                                            }
                                            li {
                                                a(href = "/write", classes = "flex items-center text-gray-700 hover:text-gray-900") {
                                                    i("w-4 h-4 mr-2") { attributes["data-lucide"] = "pen" }
                                                    +"写日记"
                                                }
                                            }
                                            li {
                                                a(href = "/archive", classes = "flex items-center text-gray-700 hover:text-gray-900") {
                                                    i("w-4 h-4 mr-2") { attributes["data-lucide"] = "calendar" }
                                                    +"归档"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        // 博客列表
                        div("space-y-6") {
                            blogs.forEach { blog ->
                                a(
                                    href = "/getBlogById?blogId=${blog.id}",
                                    classes = "block p-6 bg-white/90 rounded-lg shadow hover:shadow-lg transition-shadow duration-300"
                                ) {
                                    // 标题
                                    h2("text-2xl font-bold mb-2 text-gray-800") { +blog.title }

                                    // 日期
                                    div("flex items-center text-sm text-gray-500 mb-4") {
                                        i("w-4 h-4 mr-1") { attributes["data-lucide"] = "calendar" }
                                        span { +localDateTimeFormatter.format(blog.createdAt) }
                                    }

                                    // 预览内容
                                    article("prose prose-gray dark:prose-invert max-w-none") {
                                        blog.sections.forEach { section ->
                                            h2("text-2xl font-bold mb-4") { +section.title }
                                            section.contents.forEach { content -> buildContent(content, <EMAIL>) }
                                            h2("text-xl mb-4") { +"..." }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    get("/getBlogById") {
        val blogId = call.request.queryParameters["blogId"] ?: return@get
        val blog = blogService.getBlogDetailById(blogId) ?: return@get
        call.respondHtml {
            head {
                title { +blog.title }

                // Tailwind
                script { src = "https://cdn.tailwindcss.com" }
                link(rel = "stylesheet", href = "/static/blog-styles.css", type = "text/css")
                script { src = "/static/blog-script.js" }
                // 自定义网格背景
                style {
                    unsafe {
                        +"""
                        body {
                            background-image:
                                linear-gradient(to right, rgba(0,0,0,0.05) 1px, transparent 1px),
                                linear-gradient(to bottom, rgba(0,0,0,0.05) 1px, transparent 1px);
                            background-size: 24px 24px;
                        }
                    """.trimIndent()
                    }
                }

                // Lucide
                script { src = "https://unpkg.com/lucide@latest" }
                script {
                    unsafe {
                        +"""
                        document.addEventListener("DOMContentLoaded", function() {
                            lucide.createIcons();
                        });
                    """.trimIndent()
                    }
                }
            }
            body("text-gray-900 font-serif leading-relaxed") {
                div("container mx-auto px-4 md:px-6 lg:px-8 max-w-6xl bg-warm/70 rounded-lg shadow-lg backdrop-blur-sm") {

                    // 标题部分
                    header("py-8 border-b border-gray-200 dark:border-gray-800") {
                        h1("text-3xl font-bold mb-2") { +blog.title }
                        div("flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400") {
                            div("flex items-center") {
                                i("w-4 h-4 mr-1") { attributes["data-lucide"] = "calendar" }
                                span { +localDateTimeFormatter.format(blog.createdAt) }
                            }
                            div("flex items-center") {
                                i("w-4 h-4 mr-1") { attributes["data-lucide"] = "eye" }
                                span { +"${blog.readCount} read" }
                            }
                            div("flex items-center") {
                                i("w-4 h-4 mr-1") { attributes["data-lucide"] = "tag" }
                                span { +blog.tags.joinToString(", ") }
                            }
                        }
                    }

                    // 主体内容
                    main("py-8 lg:flex lg:gap-8 w-full") {
                        // 侧边目录
                        aside("lg:w-1/5 mb-8 lg:mb-0") {
                            div("sticky top-8") {
                                div("p-4 border border-gray-200 dark:border-gray-800 rounded-lg bg-gray-50/80 dark:bg-gray-800") {
                                    h2("text-lg font-bold mb-4 flex items-center") {
                                        i("w-5 h-5 mr-2") { attributes["data-lucide"] = "list" }
                                        +"Table of Contents"
                                    }
                                    nav("toc text-sm") {
                                        ul {
                                            blog.sections.forEach { section ->
                                                li("mb-2") {
                                                    a(href = "#${section.href}") {
                                                        classes = setOf("text-gray-700", "dark:text-gray-300", "hover:text-gray-900", "dark:hover:text-white", "transition")
                                                        +section.title
                                                    }
                                                }
                                                section.children.forEach { child ->
                                                    li("mb-2 ml-4") {
                                                        a(href = "#${child.href}") {
                                                            classes = setOf("text-gray-700", "dark:text-gray-300", "hover:text-gray-900", "dark:hover:text-white", "transition")
                                                            +child.title
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                div("mt-4 p-2 border border-gray-200 dark:border-gray-800 rounded-lg bg-gray-50/80 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition") {
                                    a(href = "/", classes = "flex items-center text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white") {
                                        i("w-4 h-4 mr-2") { attributes["data-lucide"] = "home" }
                                        +"首页"
                                    }
                                }
                            }
                        }

                        // 文章内容
                        article("lg:w-3/4 prose prose-gray dark:prose-invert max-w-none") {
                            blog.sections.forEach { section ->
                                section("mb-8") {
                                    id = section.href.removePrefix("#")
                                    h2("text-2xl font-bold mb-4") { +section.title }
                                    section.contents.forEach { content -> buildContent(content, <EMAIL>) }
                                    section.children.forEach { child ->
                                        div("mt-6") {
                                            id = child.href.removePrefix("#")
                                            h3("text-xl font-bold mb-3") { +child.title }
                                            child.contents.forEach { content -> buildContent(content, <EMAIL>) }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    get("/write") {
        call.respondHtml {
            head {
                title { +"写文章" }
                script { src = "https://cdn.tailwindcss.com" }
                script { src = "https://unpkg.com/lucide@latest" }
                script { src = "/static/write-script.js" }
                style {
                    unsafe {
                        +"""
                        body {
                            background-image:
                                linear-gradient(to right, rgba(0,0,0,0.05) 1px, transparent 1px),
                                linear-gradient(to bottom, rgba(0,0,0,0.05) 1px, transparent 1px);
                            background-size: 24px 24px;
                        }
                        .section-block {
                            border: 1px solid #e5e7eb;
                            padding: 1.5rem;
                            border-radius: 0.75rem;
                            margin-bottom: 1.5rem;
                            background-color: #fff;
                            box-shadow: 0 1px 3px rgba(0,0,0,0.1), 0 1px 2px rgba(0,0,0,0.06);
                            transition: all 0.2s ease;
                        }
                        .section-block:hover {
                            box-shadow: 0 4px 6px rgba(0,0,0,0.1), 0 2px 4px rgba(0,0,0,0.06);
                            border-color: #d1d5db;
                        }
                        .content-block {
                            margin-top: 0.75rem;
                            padding: 1rem;
                            background: #f9fafb;
                            border-radius: 0.5rem;
                            border: 1px solid #e5e7eb;
                            transition: all 0.2s ease;
                        }
                        .content-block:hover {
                            background: #f3f4f6;
                            border-color: #d1d5db;
                        }
                        .icon-btn {
                            position: relative;
                            width: 2.5rem;
                            height: 2.5rem;
                            border-radius: 50%;
                            border: 2px solid #e5e7eb;
                            background: transparent;
                            cursor: pointer;
                            transition: all 0.2s ease;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                        .icon-btn:hover {
                            transform: translateY(-2px);
                            border-color: #d1d5db;
                            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                        }
                        .icon-btn.primary {
                            border-color: #3b82f6;
                            color: #3b82f6;
                        }
                        .icon-btn.primary:hover {
                            border-color: #2563eb;
                            color: #2563eb;
                            background: rgba(59, 130, 246, 0.05);
                        }
                        .icon-btn.success {
                            border-color: #10b981;
                            color: #10b981;
                        }
                        .icon-btn.success:hover {
                            border-color: #059669;
                            color: #059669;
                            background: rgba(16, 185, 129, 0.05);
                        }
                        .icon-btn.warning {
                            border-color: #f59e0b;
                            color: #f59e0b;
                        }
                        .icon-btn.warning:hover {
                            border-color: #d97706;
                            color: #d97706;
                            background: rgba(245, 158, 11, 0.05);
                        }
                        .icon-btn.danger {
                            border-color: #ef4444;
                            color: #ef4444;
                        }
                        .icon-btn.danger:hover {
                            border-color: #dc2626;
                            color: #dc2626;
                            background: rgba(239, 68, 68, 0.05);
                        }
                        .icon-btn .tooltip {
                            position: absolute;
                            left: 3rem;
                            top: 50%;
                            transform: translateY(-50%);
                            background: #1f2937;
                            color: white;
                            font-size: 0.75rem;
                            padding: 0.375rem 0.75rem;
                            border-radius: 0.375rem;
                            opacity: 0;
                            pointer-events: none;
                            transition: all 0.2s ease;
                            white-space: nowrap;
                            z-index: 1000;
                            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                        }
                        .icon-btn:hover .tooltip {
                            opacity: 1;
                            transform: translateY(-50%) translateX(4px);
                        }
                        .icon-btn .tooltip::before {
                            content: '';
                            position: absolute;
                            right: 100%;
                            top: 50%;
                            transform: translateY(-50%);
                            border: 5px solid transparent;
                            border-right-color: #1f2937;
                        }
                    """.trimIndent()
                    }
                }
                script {
                    unsafe {
                        +"""
                        document.addEventListener("DOMContentLoaded", function() {
                            lucide.createIcons();
                        });
                    """.trimIndent()
                    }
                }
            }

            body("text-gray-900 font-serif leading-relaxed") {
                div("max-w-7xl mx-auto px-6 py-12") {
                    h1("text-4xl font-bold mb-12 text-center tracking-tight") { +"✏️ 写文章" }

                    // 主体布局
                    main("lg:flex lg:gap-10 w-full") {
                        div("sticky top-8 space-y-6") {
                            // 控制区
                            div("p-6 border border-gray-200 rounded-xl bg-gray-50 shadow-sm") {
                                h2("text-lg font-semibold mb-4 flex items-center gap-2") {
                                    i("w-5 h-5 text-gray-600") { attributes["data-lucide"] = "settings" }
                                    +"操作面板"
                                }
                                // 动态按钮区
                                div("flex flex-col gap-3") {
                                    id = "dynamicButtonsContainer"
                                    button(type = ButtonType.button) {
                                        id = "addSectionBtn"
                                        classes = setOf("addSectionBtn icon-btn primary group relative")
                                        i("w-5 h-5") { attributes["data-lucide"] = "book-plus" }
                                        span("tooltip") { +"添加章节" }
                                    }
                                }

                                // 工具按钮区
                                div("mt-6 pt-4 border-t border-gray-200") {
                                    h3("text-sm font-medium text-gray-600 mb-3") { +"工具" }
                                    div("flex flex-col gap-3") {
                                        button(type = ButtonType.button) {
                                            id = "previewBtn"
                                            classes = setOf("icon-btn warning group relative")
                                            i("w-5 h-5") { attributes["data-lucide"] = "eye" }
                                            span("tooltip") { +"预览文章" }
                                        }
                                        button(type = ButtonType.button) {
                                            id = "clearAllBtn"
                                            classes = setOf("icon-btn danger group relative")
                                            i("w-5 h-5") { attributes["data-lucide"] = "trash-2" }
                                            span("tooltip") { +"清空所有" }
                                        }
                                    }
                                }
                            }
                            // 首页链接
                            div("mt-4 p-2 border border-gray-200 dark:border-gray-800 rounded-lg bg-gray-50/80 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition") {
                                a(href = "/", classes = "flex items-center text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white") {
                                    i("w-4 h-4 mr-2") { attributes["data-lucide"] = "home" }
                                    +"首页"
                                }
                            }
                        }

                        // 右侧文章编辑区
                        div("selectable-block blogContainer") {
                            id = "blogContainer"
                            form(action = "/submitBlog", method = FormMethod.post, classes = "space-y-8 bg-white p-8 rounded-xl shadow-lg") {
                                id = "blogForm"

                                // 标题
                                div {
                                    label("block text-sm font-medium text-gray-700 mb-1") { +"标题" }
                                    textInput(name = "title") {
                                        classes = setOf("block w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-400 outline-none transition")
                                        placeholder = "输入文章标题..."
                                    }
                                }

                                // 标签
                                div {
                                    label("block text-sm font-medium text-gray-700 mb-1") { +"标签（用逗号分隔）" }
                                    textInput(name = "tags") {
                                        classes = setOf("block w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-400 outline-none transition")
                                        placeholder = "例如：生活, 旅行"
                                    }
                                }

                                // 动态章节容器
                                div { id = "sectionsContainer" }

                                submitInput {
                                    value = "🚀 发布文章"
                                    classes = setOf("w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg mt-3 transition shadow-sm cursor-pointer")
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    post("/submitBlog") {
        val params = call.receiveParameters()

        val blog = BlogEntry().apply {
            title = params["title"] ?: ""
            createdAt = LocalDateTime.now()
            tags = params["tags"]?.split(",")?.map { it.trim() } ?: emptyList()

            // 遍历 sections
            var sectionIdx = 0
            while (true) {
                val sectionTitle = params["sections[$sectionIdx][title]"] ?: break
                val section = SectionEntry().apply {
                    href = sectionIdx.toString()
                    title = sectionTitle
                    sortOrder = sectionIdx + 1
                    contents.addAll(getContentByParams(params, "sections[$sectionIdx][contents]"))
                }
                var childIdx = 0
                while (true) {
                    val childSectionTitle = params["sections[$sectionIdx][children][$childIdx][title]"] ?: break
                    val childSection = SectionEntry().apply {
                        href = "${sectionIdx}-${childIdx}"
                        title = childSectionTitle
                        sortOrder = childIdx + 1
                        contents.addAll(getContentByParams(params, "sections[${sectionIdx}-${childIdx}][contents]"))
                    }
                    section.children.add(childSection)
                    childIdx++
                }
                sections.add(section)
                sectionIdx++
            }
        }

        blogService.create(blog)
        println(objectMapper.writeValueAsString(blog))

//        call.respondText("文章已发布: ${blog.title}")
        call.respondRedirect("/getBlogById?blogId=${blog.id}")
    }

}