package org.example.routes

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.KotlinModule
import io.ktor.http.Parameters
import io.ktor.server.html.respondHtml
import io.ktor.server.request.receiveParameters
import io.ktor.server.response.respondRedirect
import io.ktor.server.response.respondText
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import kotlinx.html.*
import org.example.model.BlogEntry
import org.example.model.ContentEntry
import org.example.model.ContentType
import org.example.model.SectionEntry
import org.example.model.buildContent
import org.example.model.testBlogEntry
import org.example.table.BlogService
import org.example.util.BlogUtil.getContentByParams
import org.example.util.localDateTimeFormatter
import org.example.util.objectMapper
import org.example.uuid
import java.text.SimpleDateFormat
import java.time.LocalDateTime

fun Route.blogRoutes(blogService: BlogService) {
    get("/") {
        val blogs = blogService.getAllBlogs()
        call.respondHtml {
            head {
                title { +"My Diary" }
                meta(charset = "utf-8")
                meta(name = "viewport", content = "width=device-width, initial-scale=1.0")

                // Tailwind
                script { src = "https://cdn.tailwindcss.com" }
                script {
                    unsafe {
                        +"""
                    tailwind.config = {
                        theme: {
                            extend: {
                                colors: {
                                    warm: '#FAF3E0'
                                }
                            }
                        }
                    }
                    """.trimIndent()
                    }
                }

                // 白色背景 + 网格
                style {
                    unsafe {
                        +"""
                    body {
                        background-color: white;
                        background-image:
                            linear-gradient(to right, rgba(0,0,0,0.03) 1px, transparent 1px),
                            linear-gradient(to bottom, rgba(0,0,0,0.03) 1px, transparent 1px);
                        background-size: 24px 24px;
                    }
                    """.trimIndent()
                    }
                }

                // Lucide
                script { src = "https://unpkg.com/lucide@latest" }
                script {
                    unsafe {
                        +"""
                    document.addEventListener("DOMContentLoaded", () => {
                        lucide.createIcons();
                    });
                    """.trimIndent()
                    }
                }
            }
            body("text-gray-900 font-serif leading-relaxed") {
                // 外层容器
                div("max-w-6xl mx-auto px-4 py-12 mt-12") {
                    h1("text-4xl font-bold mb-8 text-center") { +"My Diary" }

                    // 主体内容
                    main("py-8 lg:flex lg:gap-8 w-full") {
                        // 侧边目录
                        aside("lg:w-1/5 mb-8 lg:mb-0") {
                            div("sticky top-8") {
                                div("p-4 border border-gray-200 dark:border-gray-800 rounded-lg bg-gray-50/80 dark:bg-gray-800") {
                                    h2("text-lg font-bold mb-4 flex items-center") {
                                        i("w-5 h-5 mr-2") { attributes["data-lucide"] = "list" }
                                        +"Table of Contents"
                                    }
                                    nav("toc text-sm") {
                                        h2("text-lg font-bold mb-4") { +"菜单" }
                                        ul("space-y-2") {
                                            li {
                                                a(href = "/", classes = "flex items-center text-gray-700 hover:text-gray-900") {
                                                    i("w-4 h-4 mr-2") { attributes["data-lucide"] = "home" }
                                                    +"首页"
                                                }
                                            }
                                            li {
                                                a(href = "/write", classes = "flex items-center text-gray-700 hover:text-gray-900") {
                                                    i("w-4 h-4 mr-2") { attributes["data-lucide"] = "pen" }
                                                    +"写日记"
                                                }
                                            }
                                            li {
                                                a(href = "/archive", classes = "flex items-center text-gray-700 hover:text-gray-900") {
                                                    i("w-4 h-4 mr-2") { attributes["data-lucide"] = "calendar" }
                                                    +"归档"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        // 博客列表
                        div("space-y-6") {
                            blogs.forEach { blog ->
                                a(
                                    href = "/getBlogById?blogId=${blog.id}",
                                    classes = "block p-6 bg-white/90 rounded-lg shadow hover:shadow-lg transition-shadow duration-300"
                                ) {
                                    // 标题
                                    h2("text-2xl font-bold mb-2 text-gray-800") { +blog.title }

                                    // 日期
                                    div("flex items-center text-sm text-gray-500 mb-4") {
                                        i("w-4 h-4 mr-1") { attributes["data-lucide"] = "calendar" }
                                        span { +localDateTimeFormatter.format(blog.createdAt) }
                                    }

                                    // 预览内容
                                    article("prose prose-gray dark:prose-invert max-w-none") {
                                        blog.sections.forEach { section ->
                                            h2("text-2xl font-bold mb-4") { +section.title }
                                            section.contents.forEach { content -> buildContent(content, <EMAIL>) }
                                            h2("text-xl mb-4") { +"..." }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    get("/getBlogById") {
        val blogId = call.request.queryParameters["blogId"] ?: return@get
        val blog = blogService.getBlogDetailById(blogId) ?: return@get
        call.respondHtml {
            head {
                title { +blog.title }

                // Tailwind
                script { src = "https://cdn.tailwindcss.com" }
                link(rel = "stylesheet", href = "/static/blog-styles.css", type = "text/css")
                script { src = "/static/blog-script.js" }
                // 自定义网格背景
                style {
                    unsafe {
                        +"""
                        body {
                            background-image:
                                linear-gradient(to right, rgba(0,0,0,0.05) 1px, transparent 1px),
                                linear-gradient(to bottom, rgba(0,0,0,0.05) 1px, transparent 1px);
                            background-size: 24px 24px;
                        }
                    """.trimIndent()
                    }
                }

                // Lucide
                script { src = "https://unpkg.com/lucide@latest" }
                script {
                    unsafe {
                        +"""
                        document.addEventListener("DOMContentLoaded", function() {
                            lucide.createIcons();
                        });
                    """.trimIndent()
                    }
                }
            }
            body("text-gray-900 font-serif leading-relaxed") {
                div("container mx-auto px-4 md:px-6 lg:px-8 max-w-6xl bg-warm/70 rounded-lg shadow-lg backdrop-blur-sm") {

                    // 标题部分
                    header("py-8 border-b border-gray-200 dark:border-gray-800") {
                        h1("text-3xl font-bold mb-2") { +blog.title }
                        div("flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400") {
                            div("flex items-center") {
                                i("w-4 h-4 mr-1") { attributes["data-lucide"] = "calendar" }
                                span { +localDateTimeFormatter.format(blog.createdAt) }
                            }
                            div("flex items-center") {
                                i("w-4 h-4 mr-1") { attributes["data-lucide"] = "eye" }
                                span { +"${blog.readCount} read" }
                            }
                            div("flex items-center") {
                                i("w-4 h-4 mr-1") { attributes["data-lucide"] = "tag" }
                                span { +blog.tags.joinToString(", ") }
                            }
                        }
                    }

                    // 主体内容
                    main("py-8 lg:flex lg:gap-8 w-full") {
                        // 侧边目录
                        aside("lg:w-1/5 mb-8 lg:mb-0") {
                            div("sticky top-8") {
                                div("p-4 border border-gray-200 dark:border-gray-800 rounded-lg bg-gray-50/80 dark:bg-gray-800") {
                                    h2("text-lg font-bold mb-4 flex items-center") {
                                        i("w-5 h-5 mr-2") { attributes["data-lucide"] = "list" }
                                        +"Table of Contents"
                                    }
                                    nav("toc text-sm") {
                                        ul {
                                            blog.sections.forEach { section ->
                                                li("mb-2") {
                                                    a(href = "#${section.href}") {
                                                        classes = setOf("text-gray-700", "dark:text-gray-300", "hover:text-gray-900", "dark:hover:text-white", "transition")
                                                        +section.title
                                                    }
                                                }
                                                section.children.forEach { child ->
                                                    li("mb-2 ml-4") {
                                                        a(href = "#${child.href}") {
                                                            classes = setOf("text-gray-700", "dark:text-gray-300", "hover:text-gray-900", "dark:hover:text-white", "transition")
                                                            +child.title
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            div("menu-container") {
                            }
                        }

                        // 文章内容
                        article("lg:w-3/4 prose prose-gray dark:prose-invert max-w-none") {
                            blog.sections.forEach { section ->
                                section("mb-8") {
                                    id = section.href.removePrefix("#")
                                    h2("text-2xl font-bold mb-4") { +section.title }
                                    section.contents.forEach { content -> buildContent(content, <EMAIL>) }
                                    section.children.forEach { child ->
                                        div("mt-6") {
                                            id = child.href.removePrefix("#")
                                            h3("text-xl font-bold mb-3") { +child.title }
                                            child.contents.forEach { content -> buildContent(content, <EMAIL>) }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    get("/write") {
        call.respondHtml {
            head {
                title { +"写文章" }
                script { src = "https://cdn.tailwindcss.com" }
                script { src = "/static/write-script.js" } // 引用外部 JS
                style {
                    unsafe {
                        +"""
                    .section-block { border: 1px solid #ddd; padding: 1rem; border-radius: 0.5rem; margin-bottom: 1rem; background-color: #fff; }
                    .content-block { margin-top: 0.5rem; padding: 0.5rem; background: #f9f9f9; border-radius: 0.25rem; }
                    """.trimIndent()
                    }
                }
            }
            body("bg-gray-100 p-8 font-serif") {
                h1("text-3xl font-bold mb-6") { +"写文章" }

                form(action = "/submitBlog", method = FormMethod.post, classes = "space-y-6 bg-white p-6 rounded shadow") {
                    id = "blogForm"
                    div {
                        label("block text-sm font-medium text-gray-700") { +"标题" }
                        textInput(name = "title") {
                            classes = setOf("mt-1 block w-full border border-gray-300 rounded p-2")
                            placeholder = "输入文章标题"
                        }
                    }

                    div {
                        label("block text-sm font-medium text-gray-700") { +"标签（用逗号分隔）" }
                        textInput(name = "tags") {
                            classes = setOf("mt-1 block w-full border border-gray-300 rounded p-2")
                            placeholder = "例如：生活, 旅行"
                        }
                    }

                    div { id = "sectionsContainer" }

                    button(type = ButtonType.button) {
                        id = "addSectionBtn"
                        classes = setOf("bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600")
                        +"添加章节"
                    }

                    submitInput {
                        value = "发布文章"
                        classes = setOf("bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded")
                    }
                }
            }
        }
    }

    post("/submitBlog") {
        val params = call.receiveParameters()

        val blog = BlogEntry().apply {
            title = params["title"] ?: ""
            createdAt = LocalDateTime.now()
            tags = params["tags"]?.split(",")?.map { it.trim() } ?: emptyList()

            // 遍历 sections
            var sectionIdx = 0
            while (true) {
                val sectionTitle = params["sections[$sectionIdx][title]"] ?: break
                val section = SectionEntry().apply {
                    href = sectionIdx.toString()
                    title = sectionTitle
                    sortOrder = sectionIdx + 1
                    contents.addAll(getContentByParams(params, "sections[$sectionIdx][contents]"))
                }
                var childIdx = 0
                while (true) {
                    val childSectionTitle = params["sections[$sectionIdx][children][$childIdx][title]"] ?: break
                    val childSection = SectionEntry().apply {
                        href = "${sectionIdx}-${childIdx}"
                        title = childSectionTitle
                        sortOrder = childIdx + 1
                        contents.addAll(getContentByParams(params, "sections[${sectionIdx}-${childIdx}][contents]"))
                    }
                    section.children.add(childSection)
                    childIdx++
                }
                sections.add(section)
                sectionIdx++
            }
        }

        blogService.create(blog)
        println(objectMapper.writeValueAsString(blog))

//        call.respondText("文章已发布: ${blog.title}")
        call.respondRedirect("/getBlogById?blogId=${blog.id}")
    }

}