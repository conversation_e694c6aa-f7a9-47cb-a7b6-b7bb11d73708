package org.example.table

import org.example.model.BaseEntity
import org.example.model.BaseTableService
import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.datetime
import org.jetbrains.exposed.sql.javatime.timestamp
import java.math.BigDecimal

object AisPositionTable : Table("tbl_aisposition") {
    val id = varchar("id", 36)
    val reportNumber = integer("reportNumber")
    val shipId = varchar("shipId", 36).nullable()
    val mmsi = varchar("mmsi", 100).nullable()
    val navigationStatusCode = varchar("navigationStatusCode", 10).nullable()
    val rateOfTurn = integer("rateOfTurn").nullable()
    val speedOverGround = float("speedOverGround").nullable()
    val positionAccuracy = integer("positionAccuracy").nullable()
    val longitude = decimal("longitude", 9, 6)
    val latitude = decimal("latitude", 8, 6)
    val courseOverGround = float("courseOverGround").nullable()
    val heading = integer("heading").nullable()
    val second = integer("second").nullable()
    val specialManeuverIndicator = integer("specialManeuverIndicator").nullable()
    val raimFlag = integer("raimFlag").nullable()
    val reportTime = datetime("reportTime")
    val rawData = varchar("rawData", 500).nullable()
    val sysCreated = timestamp("sysCreated")
    val sysUpdated = varchar("sysUpdated", 100).nullable()
    val sysDeleted = bool("sysDeleted").nullable()
    val fromType = varchar("fromType", 2).nullable()
    override val primaryKey = PrimaryKey(id)
}

class AisPosition : BaseEntity(AisPositionTable) {
    var reportNumber: Int = 0
    var shipId: String? = null
    var mmsi: String? = null
    var navigationStatusCode: String? = null
    var rateOfTurn: Int? = null
    var speedOverGround: Float? = null
    var positionAccuracy: Int? = null
    var longitude: BigDecimal = BigDecimal.ZERO
    var latitude: BigDecimal = BigDecimal.ZERO
    var courseOverGround: Float? = null
    var heading: Int? = null
    var second: Int? = null
    var specialManeuverIndicator: Int? = null
    var raimFlag: Int? = null
    var reportTime: String = ""
    var rawData: String? = null
    var sysCreated: String = ""
    var sysUpdated: String? = null
    var sysDeleted: Boolean? = null
    var fromType: String? = null
}

fun ResultRow.toAisPosition() = AisPosition().apply {
    id = this@toAisPosition[AisPositionTable.id]
    reportNumber = this@toAisPosition[AisPositionTable.reportNumber]
    shipId = this@toAisPosition[AisPositionTable.shipId]
    mmsi = this@toAisPosition[AisPositionTable.mmsi]
    navigationStatusCode = this@toAisPosition[AisPositionTable.navigationStatusCode]
    rateOfTurn = this@toAisPosition[AisPositionTable.rateOfTurn]
    speedOverGround = this@toAisPosition[AisPositionTable.speedOverGround]
    positionAccuracy = this@toAisPosition[AisPositionTable.positionAccuracy]
    longitude = this@toAisPosition[AisPositionTable.longitude]
    latitude = this@toAisPosition[AisPositionTable.latitude]
    courseOverGround = this@toAisPosition[AisPositionTable.courseOverGround]
    heading = this@toAisPosition[AisPositionTable.heading]
    second = this@toAisPosition[AisPositionTable.second]
    specialManeuverIndicator = this@toAisPosition[AisPositionTable.specialManeuverIndicator]
    raimFlag = this@toAisPosition[AisPositionTable.raimFlag]
    reportTime = this@toAisPosition[AisPositionTable.reportTime].toString()
    rawData = this@toAisPosition[AisPositionTable.rawData]
    sysCreated = this@toAisPosition[AisPositionTable.sysCreated].toString()
    sysUpdated = this@toAisPosition[AisPositionTable.sysUpdated]
    sysDeleted = this@toAisPosition[AisPositionTable.sysDeleted]
    fromType = this@toAisPosition[AisPositionTable.fromType]
}

class AisPositionService : BaseTableService<AisPosition, String, AisPositionTable>(AisPositionTable) {
    override val idColumn: Column<String> = AisPositionTable.id

    override fun ResultRow.toEntity() = this.toAisPosition()
}