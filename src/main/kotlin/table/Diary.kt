package org.example.table

import org.example.util.localDateTimeFormatter
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.javatime.datetime
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.transactions.transaction
import java.time.LocalDateTime
import kotlin.collections.get

object Diaries : Table("diary") {
    val id = integer("id").autoIncrement()
    val title = varchar("title", 255)
    val content = text("content")
    val createdAt = datetime("created_at")

    override val primaryKey = PrimaryKey(id)
}

data class DiaryEntry(val id: Int, val title: String, val content: String, val createdAt: String)

object Diary {
    fun add(title: String, content: String) {
        transaction {
            Diaries.insert {
                it[Diaries.title] = title
                it[Diaries.content] = content
                it[Diaries.createdAt] = LocalDateTime.now()
            }
        }
    }

    fun all(): List<DiaryEntry> = transaction {
        Diaries.selectAll().map {
            DiaryEntry(it[Diaries.id], it[Diaries.title], it[Diaries.content], localDateTimeFormatter.format(it[Diaries.createdAt]))
        }
    }

    fun get(id: Int): DiaryEntry? = transaction {
        Diaries.select { Diaries.id eq id }.map {
            DiaryEntry(it[Diaries.id], it[Diaries.title], it[Diaries.content], localDateTimeFormatter.format(it[Diaries.createdAt]))
        }.singleOrNull()
    }
}