package org.example.table

import org.example.model.BaseTableService
import org.example.model.BlogEntry
import org.example.model.ContentEntry
import org.example.model.SectionEntry
import org.example.util.insertSorted
import org.example.util.objectMapper
import org.example.uuid
import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.datetime
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.selectAll

object BlogTable : Table("blog") {
    val id = varchar("id", 32)
    val title = varchar("title", 255)
    val createdAt = datetime("created_at")
    val readCount = integer("read_count")
}

object SectionTable : Table("section") {
    val id = varchar("id", 32)
    val blogId = varchar("blog_id", 32)
    val title = varchar("title", 255)
    val href = varchar("href", 255)
    var parentId = varchar("parent_id", 32)
    var sortOrder = integer("sort_order")
}

object ContentTable : Table("content") {
    val id = varchar("id", 32)
    val sectionId = varchar("section_id", 32)
    val type = integer("type")
    val href = varchar("href", 255)
    val value = text("value")
    val classes = text("classes")
    var parentId = varchar("parent_id", 32)
    var sortOrder = integer("sort_order")
}

fun ResultRow.toBlogEntry() = BlogEntry().apply {
    id = this@toBlogEntry[BlogTable.id]
    title = this@toBlogEntry[BlogTable.title]
    createdAt = this@toBlogEntry[BlogTable.createdAt]
    readCount = this@toBlogEntry[BlogTable.readCount]
}

fun ResultRow.toSectionEntry() = SectionEntry().apply {
    id = this@toSectionEntry[SectionTable.id]
    blogId = this@toSectionEntry[SectionTable.blogId]
    title = this@toSectionEntry[SectionTable.title]
    href = this@toSectionEntry[SectionTable.href]
    parentId = this@toSectionEntry[SectionTable.parentId]
    sortOrder = this@toSectionEntry[SectionTable.sortOrder]
}

fun ResultRow.toContentEntry() = ContentEntry().apply {
    id = this@toContentEntry[ContentTable.id]
    sectionId = this@toContentEntry[ContentTable.sectionId]
    type = this@toContentEntry[ContentTable.type]
    href = this@toContentEntry[ContentTable.href]
    value = this@toContentEntry[ContentTable.value]
    classes = this@toContentEntry[ContentTable.classes]
    parentId = this@toContentEntry[ContentTable.parentId]
    sortOrder = this@toContentEntry[ContentTable.sortOrder]
}

fun buildSectionTree(sections: List<SectionEntry>): List<SectionEntry> {
    val sectionMap = sections.associateBy { it.id } // 方便根据 id 查对象
    val roots = mutableListOf<SectionEntry>()

    for (section in sections) {
        if (section.parentId == section.blogId) {
            // 没有 parentId，是根节点
            roots.insertSorted(section, compareBy { it.sortOrder })
        } else {
            // 找到父节点，加到父节点 children
            sectionMap[section.parentId]?.children?.insertSorted(section, compareBy { it.sortOrder })
        }
    }
    return roots
}

fun buildContentTree(contents: List<ContentEntry>): List<ContentEntry> {
    val contentMap = contents.associateBy { it.id }
    val roots = mutableListOf<ContentEntry>()

    for (content in contents) {
        if (content.parentId == content.sectionId) {
            roots.insertSorted(content, compareBy { it.sortOrder })
        } else {
            contentMap[content.parentId]?.children?.insertSorted(content, compareBy { it.sortOrder })
        }
    }
    return roots
}

class BlogService(private val sectionService: SectionService, private val contentService: ContentService) : BaseTableService<BlogEntry, String, BlogTable>(BlogTable) {
    override val idColumn: Column<String> = BlogTable.id

    override fun ResultRow.toEntity() = this.toBlogEntry()

    suspend fun getBlogDetailById(id: String): BlogEntry? = dbQuery {
        val blog = BlogTable.select { BlogTable.id eq id }.singleOrNull()?.toBlogEntry() ?: return@dbQuery null
        val sections = SectionTable.select { SectionTable.blogId eq id }.map { it.toSectionEntry() }
        val contents = ContentTable.select { ContentTable.sectionId.inList(sections.map { it.id })}.map { it.toContentEntry() }

        buildBlogTree(blog, sections, contents)

        blog
    }

    suspend fun getAllBlogs(): List<BlogEntry> = dbQuery {
        val blogs = BlogTable.selectAll().orderBy(BlogTable.createdAt, org.jetbrains.exposed.sql.SortOrder.DESC).limit(5, 0).map { it.toBlogEntry() }
        val sections = SectionTable.select { SectionTable.parentId.inList(blogs.map { it.id }) }.map { it.toSectionEntry() }
        val contents = ContentTable.select { ContentTable.parentId.inList(sections.map { it.id }) }.map { it.toContentEntry() }

        val sectionGroup = sections.groupBy { it.blogId }
        val contentGroup = contents.groupBy { it.sectionId }

        blogs.forEach { blog ->
            val blogSections = sectionGroup.getOrDefault(blog.id, emptyList())
            buildBlogTree(blog, sectionGroup.getOrDefault(blog.id, emptyList()), contentGroup.filter { it.key in blogSections.map { it.id } }.values.flatten())
        }

        blogs
    }

    fun buildBlogTree(blog: BlogEntry, sections: List<SectionEntry>, contents: List<ContentEntry>) {
        // Section 树
        val sectionTree = buildSectionTree(sections)

        // Content 树
        val contentTree = buildContentTree(contents).groupBy { it.sectionId }

        sections.forEach { section ->
            section.contents.addAll(contentTree[section.id] ?: emptyList())
        }

        blog.apply { this.sections = sectionTree.toMutableList() }
    }

    override suspend fun create(entity: BlogEntry): BlogEntry {
        val sections = mutableListOf<SectionEntry>()
        val contents = mutableListOf<ContentEntry>()
        entity.id = uuid()

        // 收集所有 Section 和 Content
        entity.sections.forEach { section ->
            section.id = uuid()
            section.blogId = entity.id
            section.parentId = entity.id
            collectSectionsAndContents(section, sections, contents)
        }

        sectionService.batchCreate(sections)
        contentService.batchCreate(contents)

        return super.create(entity)
    }

    private fun collectSectionsAndContents(
        section: SectionEntry,
        sectionList: MutableList<SectionEntry>,
        contentList: MutableList<ContentEntry>
    ) {
        // 收集当前 section
        sectionList.add(section)

        // 收集当前 section 的所有内容
        section.contents.forEach { content ->
            content.id = uuid()
            content.sectionId = section.id
            content.parentId = section.id
            contentList.add(content)
            collectContentChildren(content, contentList)
        }

        // 递归收集子 section
        section.children.forEach { childSection ->
            childSection.id = uuid()
            childSection.blogId = section.blogId
            childSection.parentId = section.id
            collectSectionsAndContents(childSection, sectionList, contentList)
        }
    }

    private fun collectContentChildren(content: ContentEntry, contentList: MutableList<ContentEntry>) {
        content.children.forEach { childContent ->
            childContent.id = uuid()
            childContent.sectionId = content.sectionId
            childContent.parentId = content.id
            contentList.add(childContent)
            collectContentChildren(childContent, contentList)
        }
    }

}

class SectionService : BaseTableService<SectionEntry, String, SectionTable>(SectionTable) {
    override val idColumn: Column<String> = SectionTable.id

    override fun ResultRow.toEntity() = this.toSectionEntry()
}

class ContentService : BaseTableService<ContentEntry, String, ContentTable>(ContentTable) {
    override val idColumn: Column<String> = ContentTable.id

    override fun ResultRow.toEntity() = this.toContentEntry()
}