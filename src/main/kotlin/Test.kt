package org.example

import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.example.database.HikariMariadbPool
import org.example.model.ContentEntry
import org.example.model.SectionEntry
import org.example.model.testBlogEntry
import org.example.table.BlogService
import org.example.table.ContentService
import org.example.table.SectionService
import java.time.LocalDateTime
import java.util.UUID

fun uuid(): String {
    return UUID.randomUUID().toString().replace("-", "")
}

fun main() = runBlocking {
    HikariMariadbPool

    val sectionService = SectionService()
    val contentService = ContentService()
    val service = BlogService(sectionService, contentService)


    val blogId = uuid()
    service.create(testBlogEntry.apply {
        id = blogId
        createdAt = LocalDateTime.now()
        readCount = 100
    })

    suspend fun saveContentChildren(contentEntry: ContentEntry) {
        contentEntry.children.forEachIndexed { index, child ->
            child.id = uuid()
            child.sectionId = contentEntry.sectionId
            child.parentId = contentEntry.id
            child.sortOrder = index
            contentService.create(child)

            saveContentChildren(child)
        }
    }


    suspend fun saveSectionChildren(sectionEntry: SectionEntry) {
        sectionEntry.children.forEachIndexed { index, child ->
            child.blogId = sectionEntry.blogId
            child.parentId = sectionEntry.id
            child.id = uuid()
            child.sortOrder = index
            sectionService.create(child)

            child.contents.forEachIndexed { index, content ->
                content.id = uuid()
                content.sectionId = child.id
                content.parentId = child.id
                content.sortOrder = index
                contentService.create(content)

                saveContentChildren(content)
            }

            saveSectionChildren(child)
        }
    }


    testBlogEntry.sections.forEachIndexed { index, section ->
        section.id = uuid()
        section.blogId = blogId
        section.parentId = blogId
        section.sortOrder = index
        sectionService.create(section)

        section.contents.forEachIndexed { index, content ->
            content.id = uuid()
            content.sectionId = section.id
            content.parentId = section.id
            content.sortOrder = index
            contentService.create(content)

            saveContentChildren(content)
        }
        saveSectionChildren(section)
    }

    delay(60 * 60)
}