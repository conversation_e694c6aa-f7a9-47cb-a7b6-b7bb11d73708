package org.example

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import freemarker.cache.ClassTemplateLoader
import io.ktor.serialization.jackson.jackson
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.engine.embeddedServer
import io.ktor.server.freemarker.FreeMarker
import io.ktor.server.http.content.staticResources
import io.ktor.server.netty.Netty
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.response.respondText
import io.ktor.server.routing.get
import io.ktor.server.routing.routing
import org.example.database.HikariMariadbPool
import org.example.model.crudRoutes
import org.example.routes.blogRoutes
import org.example.table.BlogService
import org.example.table.ContentService
import org.example.table.SectionService
import org.example.util.localDateTimeFormatter
import java.text.SimpleDateFormat
import java.time.format.DateTimeFormatter

fun main() {
    embeddedServer(Netty, port = 8004, module = Application::module).start(wait = true)
}
//TIP To <b>Run</b> code, press <shortcut actionId="Run"/> or
// click the <icon src="AllIcons.Actions.Execute"/> icon in the gutter.
fun Application.module() {
    // 初始化数据库
    HikariMariadbPool

    val sectionService = SectionService()
    val contentService = ContentService()
    val blogService = BlogService(sectionService, contentService)

    install(FreeMarker) {
        templateLoader = ClassTemplateLoader(this::class.java.classLoader, "templates")
    }

    install(ContentNegotiation) {
        jackson {
            registerModule(JavaTimeModule())
            enable(SerializationFeature.INDENT_OUTPUT)
            // 自定义日期格式
            dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
            disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        }
    }

    routing {
        staticResources("/static", "static")

        blogRoutes(blogService)

        crudRoutes(
            path = "blog",
            service = blogService,
            idParser = { it }
        )

        crudRoutes(
            path = "section",
            service = SectionService(),
            idParser = { it }
        )

        crudRoutes(
            path = "content",
            service = ContentService(),
            idParser = { it }
        )
    }
}