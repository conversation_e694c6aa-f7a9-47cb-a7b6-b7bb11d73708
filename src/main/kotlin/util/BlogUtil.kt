package org.example.util

import io.ktor.http.Parameters
import org.example.model.ContentEntry
import org.example.model.ContentType

object BlogUtil {
    fun getContentByParams(params: Parameters, prefix: String): List<ContentEntry> {
        val contents = mutableListOf<ContentEntry>()
        var contentIdx = 0
        while (true) {
            val value = params["$prefix[$contentIdx][value]"] ?: break
            val content = ContentEntry().apply {
                type = ContentType.P.value
                classes = "mb-3"
                this.value = value
                sortOrder = contentIdx + 1
            }
            contents.add(content)
            contentIdx++
        }
        return contents
    }
}