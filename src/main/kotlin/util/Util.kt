package org.example.util

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import java.text.SimpleDateFormat
import java.time.format.DateTimeFormatter

val localDateTimeFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")

fun <T> MutableList<T>.insertSorted(element: T, comparator: Comparator<T>) {
    val index = this.binarySearch(element, comparator)
        .let { if (it < 0) -(it + 1) else it }
    this.add(index, element)
}

val objectMapper = ObjectMapper().apply {
    configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
    configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false)
    setSerializationInclusion(JsonInclude.Include.NON_NULL)
    dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm")
    registerModule(JavaTimeModule())
}

