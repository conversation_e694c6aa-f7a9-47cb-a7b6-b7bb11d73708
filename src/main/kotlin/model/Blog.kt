package org.example.model

import kotlinx.html.TagConsumer
import kotlinx.html.html
import kotlinx.html.*
import kotlinx.html.stream.appendHTML
import org.example.table.BlogTable
import org.example.table.ContentTable
import org.example.table.SectionTable
import java.time.LocalDateTime

class BlogEntry : BaseEntity(BlogTable) {
    var title: String = ""
    var createdAt: LocalDateTime? = null
    var readCount: Int = 0
    var tags: List<String> = emptyList()
    var sections: MutableList<SectionEntry> = mutableListOf()
}

class SectionEntry : BaseEntity(SectionTable) {
    var href: String = ""
    var title: String = ""
    var contents: MutableList<ContentEntry> = mutableListOf()
    var children: MutableList<SectionEntry> = mutableListOf()

    var blogId: String = ""
    var parentId: String = ""
    var sortOrder: Int = 0
}

class ContentEntry : BaseEntity(ContentTable) {
    var type: Int = 0
    var classes: String = ""
    var href: String = ""
    var value: String = ""
    var children: MutableList<ContentEntry> = mutableListOf()

    var sectionId: String = ""
    var parentId: String = ""
    var sortOrder: Int = 0
}

enum class ContentType(val value: Int) {
    DIV(0),
    P(1),
    LI(2),
    A(3),
    UL(4),
    TABLE(5),
    THEAD(6),
    TBODY(7),
    TR(8),
    TH(9),
    TD(10),
    PRE(11),
    CODE(12),
    FIGURE(13),
    IMG(14),
    FIGCAPTION(15)
}

fun main() {
    System.out.appendHTML().html {

    }
}

fun buildContent(content: ContentEntry, consumer: TagConsumer<*>) {
    when (content.type) {
        ContentType.DIV.value -> {
            consumer.div(content.classes) {
                +content.value
                content.children.forEach { child -> buildContent(child, consumer) }
            }
        }
        ContentType.P.value -> {
            consumer.p(content.classes) {
                +content.value
                content.children.forEach { child -> buildContent(child, consumer) }
            }
        }
        ContentType.LI.value -> {
            consumer.li(content.classes) {
                +content.value
                content.children.forEach { child -> buildContent(child, consumer) }
            }
        }
        ContentType.A.value -> {
            consumer.a {
                classes = content.classes.split(" ").toSet()
                href = content.href
                +content.value
                content.children.forEach { child -> buildContent(child, consumer) }
            }
        }
        ContentType.UL.value -> {
            consumer.ul(content.classes) {
                content.children.forEach { child -> buildContent(child, consumer) }
            }
        }
        ContentType.TABLE.value -> {
            consumer.table(content.classes) {
                content.children.forEach { child -> buildContent(child, consumer) }
            }
        }
        ContentType.THEAD.value -> {
            consumer.thead(content.classes) {
                content.children.forEach { child -> buildContent(child, consumer) }
            }
        }
        ContentType.TBODY.value -> {
            consumer.tbody(content.classes) {
                content.children.forEach { child -> buildContent(child, consumer) }
            }
        }
        ContentType.TR.value -> {
            consumer.tr(content.classes) {
                content.children.forEach { child -> buildContent(child, consumer) }
            }
        }
        ContentType.TH.value -> {
            consumer.th {
                classes = content.classes.split(" ").toSet()
                +content.value
                content.children.forEach { child -> buildContent(child, consumer) }
            }
        }
        ContentType.TD.value -> {
            consumer.td(content.classes) {
                +content.value
                content.children.forEach { child -> buildContent(child, consumer) }
            }
        }
        ContentType.PRE.value -> {
            consumer.pre(content.classes) {
                +content.value.trimIndent()
                content.children.forEach { child -> buildContent(child, consumer) }
            }
        }
        ContentType.CODE.value -> {
            consumer.code(content.classes) {
                +content.value.trimIndent()
                content.children.forEach { child -> buildContent(child, consumer) }
            }
        }
        ContentType.FIGURE.value -> {
            consumer.figure(content.classes) {
                content.children.forEach { child -> buildContent(child, consumer) }
            }
        }
        ContentType.IMG.value -> {
            consumer.img(alt = content.value, src = content.href, classes = content.classes) {
                id = content.id
            }
        }
        ContentType.FIGCAPTION.value -> {
            consumer.figcaption(content.classes) {
                +content.value
                content.children.forEach { child -> buildContent(child, consumer) }
            }
        }
        else -> {
            // 默认处理或抛出异常
            consumer.span {
                +content.value
                content.children.forEach { child -> buildContent(child, consumer) }
            }
        }
    }
}

//private fun <T : HTMLTag> applyCommonAttributes(content: Content, tag: T) {
//    if (content.classes.isNotEmpty()) {
//        tag.classes = content.classes.split(" ").toSet()
//    }
//    if (content.value.isNotEmpty() && content.children.isEmpty()) {
//        tag + content.value
//    }
//}

val testBlogEntry = BlogEntry().apply {
    title = "Hello World"
    createdAt = LocalDateTime.now()
    readCount = 100
    tags = listOf("tag1", "tag2", "tag3")
    sections = mutableListOf(
        SectionEntry().apply {
            href = "introduction"
            title = "一、MCP协议核心解析"
            contents = mutableListOf(
                ContentEntry().apply {
                    type = ContentType.P.value
                    classes = "mb-3"
                    value = "我是lenz，很高兴在这里分享我的第一篇文章。近期工作中接触到了MCP的学习机会，作为一名主要使用Kotlin的后端开发者，我计划撰写MCP相关的文章。本文基于我的实际开发经验和个人理解整理而成，如有不足之处欢迎指正交流。"
                },
                ContentEntry().apply {
                    type = ContentType.P.value
                    classes = "mb-3"
                    value = "参考资料："
                },
                ContentEntry().apply {
                    type = ContentType.UL.value
                    classes = "list-disc pl-6 mb-3"
                    children = mutableListOf(
                        ContentEntry().apply {
                            type = ContentType.LI.value
                            children = mutableListOf(ContentEntry().apply {
                                type = ContentType.A.value
                                href = "https://mcp-docs.cn/introduction"
                                classes = "text-gray-500 hover:text-gray-700 hover:underline"
                                value = "MCP 中文文档"
                            })
                        },
                        ContentEntry().apply {
                            type = ContentType.LI.value
                            children = mutableListOf(ContentEntry().apply {
                                type = ContentType.A.value
                                href = "https://mcp-docs.cn/introduction"
                                classes = "text-gray-500 hover:text-gray-700 hover:underline"
                                value = "MCP 中文站"
                            })
                        },
                        ContentEntry().apply {
                            type = ContentType.LI.value
                            children = mutableListOf(ContentEntry().apply {
                                type = ContentType.A.value
                                href = "https://mcp-docs.cn/introduction"
                                classes = "text-gray-500 hover:text-gray-700 hover:underline"
                                value = "Kotlin-SDK"
                            })
                        }
                    )
                }
            )
            children = mutableListOf(
                SectionEntry().apply {
                    href = "introduction-1"
                    title = "1.1 协议定位"
                    contents = mutableListOf(
                        ContentEntry().apply {
                            type = ContentType.P.value
                            classes = "mb-3"
                            value = "MCP（Model Context Protocol）作为开放协议，其核心价值在于<strong>标准化LLM的上下文交互方式</strong>。官方将其类比为\"AI领域的USB-C接口\"——正如USB-C统一了设备连接标准，MCP为AI模型提供了统一的数据源和工具接入规范。"
                        }
                    )
                },
                SectionEntry().apply {
                    href = "introduction-2"
                    title = "1.2 技术本质"
                    contents = mutableListOf(
                        ContentEntry().apply {
                            type = ContentType.P.value
                            classes = "mb-3"
                            value = "从实现层面看，MCP实质上是<strong>对LLM Function Call功能的标准化封装</strong>，其架构包含两个关键组件："
                        },
                        ContentEntry().apply {
                            type = ContentType.UL.value
                            classes = "list-disc pl-6 mb-3"
                            children = mutableListOf(
                                ContentEntry().apply {
                                    type = ContentType.LI.value
                                    value = "服务端：实现标准化Function Call接口"
                                },
                                ContentEntry().apply {
                                    type = ContentType.LI.value
                                    value = "客户端：动态发现并调用服务端能力"
                                }
                            )
                        }
                    )
                },
                SectionEntry().apply {
                    href = "introduction-3"
                    title = "1.3 架构类比"
                    contents = mutableListOf(
                        ContentEntry().apply {
                            type = ContentType.P.value
                            classes = "mb-3"
                            value = "采用前后端分离架构的类比理解："
                        },
                        ContentEntry().apply {
                            type = ContentType.TABLE.value
                            classes = "table-auto border border-gray-300 text-left text-sm mt-2 mb-3"
                            children = mutableListOf(
                                ContentEntry().apply {
                                    type = ContentType.THEAD.value
                                    classes = "bg-gray-100"
                                    children = mutableListOf(
                                        ContentEntry().apply {
                                            type = ContentType.TR.value
                                            children = mutableListOf(
                                                ContentEntry().apply {
                                                    type = ContentType.TH.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "组件"
                                                },
                                                ContentEntry().apply {
                                                    type = ContentType.TH.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "功能对应"
                                                },
                                                ContentEntry().apply {
                                                    type = ContentType.TH.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "功能对应"
                                                }
                                            )
                                        }
                                    )
                                },
                                ContentEntry().apply {
                                    type = ContentType.TBODY.value
                                    children = mutableListOf(
                                        ContentEntry().apply {
                                            type = ContentType.TR.value
                                            children = mutableListOf(
                                                ContentEntry().apply {
                                                    type = ContentType.TD.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "MCP服务端"
                                                },
                                                ContentEntry().apply {
                                                    type = ContentType.TD.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "后端API"
                                                },
                                                ContentEntry().apply {
                                                    type = ContentType.TD.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "提供标准化能力接口"
                                                }
                                            )
                                        },
                                        ContentEntry().apply {
                                            type = ContentType.TR.value
                                            children = mutableListOf(
                                                ContentEntry().apply {
                                                    type = ContentType.TD.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "MCP客户端"
                                                },
                                                ContentEntry().apply {
                                                    type = ContentType.TD.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "前端应用"
                                                },
                                                ContentEntry().apply {
                                                    type = ContentType.TD.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "交互调度层"
                                                }
                                            )
                                        },
                                        ContentEntry().apply {
                                            type = ContentType.TR.value
                                            children = mutableListOf(
                                                ContentEntry().apply {
                                                    type = ContentType.TD.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "LLM"
                                                },
                                                ContentEntry().apply {
                                                    type = ContentType.TD.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "用户操作行为"
                                                },
                                                ContentEntry().apply {
                                                    type = ContentType.TD.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "动态触发功能调用"
                                                }
                                            )
                                        }
                                    )
                                }
                            )
                        }
                    )
                }
            )
        },
        SectionEntry().apply {
            href = "section-1"
            title = "二、Kotlin服务端实现方案"
            contents = mutableListOf()
            children = mutableListOf(
                SectionEntry().apply {
                    href = "section-1-1"
                    title = "2.1 实现选型建议"
                    contents = mutableListOf(
                        ContentEntry().apply {
                            type = ContentType.P.value
                            classes = "mb-3"
                            value = "推荐技术组合："
                        },
                        ContentEntry().apply {
                            type = ContentType.UL.value
                            classes = "list-disc pl-6 mb-3"
                            children = mutableListOf(
                                ContentEntry().apply {
                                    type = ContentType.LI.value
                                    value = "传输协议：SSE（Server-Sent Events）"
                                },
                                ContentEntry().apply {
                                    type = ContentType.LI.value
                                    value = "开发语言：Kotlin"
                                },
                                ContentEntry().apply {
                                    type = ContentType.LI.value
                                    value = "框架选择：Ktor"
                                }
                            )
                        },
                        ContentEntry().apply {
                            type = ContentType.P.value
                            classes = "mb-3"
                            value = "协议选型对比："
                        },
                        ContentEntry().apply {
                            type = ContentType.TABLE.value
                            classes = "table-auto border border-gray-300 text-left text-sm mt-2 mb-3"
                            children = mutableListOf(
                                ContentEntry().apply {
                                    type = ContentType.THEAD.value
                                    classes = "bg-gray-100"
                                    children = mutableListOf(
                                        ContentEntry().apply {
                                            type = ContentType.TR.value
                                            children = mutableListOf(
                                                ContentEntry().apply {
                                                    type = ContentType.TH.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "传输方式"
                                                },
                                                ContentEntry().apply {
                                                    type = ContentType.TH.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "启动方式"
                                                },
                                                ContentEntry().apply {
                                                    type = ContentType.TH.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "适用场景"
                                                }
                                            )
                                        }
                                    )
                                },
                                ContentEntry().apply {
                                    type = ContentType.TBODY.value
                                    children = mutableListOf(
                                        ContentEntry().apply {
                                            type = ContentType.TR.value
                                            children = mutableListOf(
                                                ContentEntry().apply {
                                                    type = ContentType.TD.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "STDIO"
                                                },
                                                ContentEntry().apply {
                                                    type = ContentType.TD.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "子进程模式"
                                                },
                                                ContentEntry().apply {
                                                    type = ContentType.TD.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "嵌入式部署"
                                                }
                                            )
                                        },
                                        ContentEntry().apply {
                                            type = ContentType.TR.value
                                            children = mutableListOf(
                                                ContentEntry().apply {
                                                    type = ContentType.TD.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "SSE"
                                                },
                                                ContentEntry().apply {
                                                    type = ContentType.TD.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "独立HTTP服务"
                                                },
                                                ContentEntry().apply {
                                                    type = ContentType.TD.value
                                                    classes = "border border-gray-300 px-4 py-2"
                                                    value = "云原生部署"
                                                }
                                            )
                                        }
                                    )
                                }
                            )
                        }
                    )
                },
                SectionEntry().apply {
                    href = "section-1-2"
                    title = "2.2 环境配置清单"
                    contents = mutableListOf(
                        ContentEntry().apply {
                            type = ContentType.PRE.value
                            classes = "bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto font-geist-mono mt-2 mb-3"
                            value = """
                                - JDK：≥17（推荐Amazon Corretto 17）
                                - 构建工具：Gradle 8.5+
                                - 关键依赖：
                                    io.modelcontextprotocol:kotlin-sdk:0.4.0
                                    kotlinx-serialization-json:1.7.x
                            """.trimIndent()
                        },
                        ContentEntry().apply {
                            type = ContentType.DIV.value
                            classes = "bg-gray-50 dark:bg-gray-800 border-l-4 border-blue-500 p-4 my-4"
                            children = mutableListOf(
                                ContentEntry().apply {
                                    type = ContentType.P.value
                                    classes = "text-sm"
                                    value = "<strong>版本适配警告</strong>：务必确保<strong>kotlin(\"jvm\")</strong>插件与序列化插件版本严格匹配（推荐2.1.0），否则可能引发运行时异常。"
                                }
                            )
                        }
                    )
                },
                SectionEntry().apply {
                    href = "section-1-3"
                    title = "2.3 服务端核心实现"
                    contents = mutableListOf(
                        ContentEntry().apply {
                            type = ContentType.P.value
                            classes = "mb-3"
                            value = "启动配置示例："
                        },
                        ContentEntry().apply {
                            type = ContentType.PRE.value
                            classes = "bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto font-geist-mono mt-2 mb-3"
                            children.add(ContentEntry().apply {
                                type = ContentType.CODE.value
                                value = """
                                    fun runSseMcpServerUsingKtorPlugin(port: Int): Unit = runBlocking {
                                        println("Starting sse server on port ${"\\$"}port")
                                        println("Use inspector to connect to the http://localhost:${"\\$"}port/sse")
                                                            
                                        embeddedServer(CIO, host = "0.0.0.0", port = port) {
                                            mcp {
                                                return@mcp configureServer()
                                            }
                                        }.start(wait = true)
                                    }
                                """.trimIndent()
                            })
                        },
                        ContentEntry().apply {
                            type = ContentType.P.value
                            classes = "mb-3"
                            value = "能力注册模板："
                        },
                        ContentEntry().apply {
                            type = ContentType.PRE.value
                            classes = "bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto font-geist-mono mt-2 mb-3"
                            children.add(ContentEntry().apply {
                                type = ContentType.CODE.value
                                value = """
                                    fun configureServer(): Server {
                                        val server = Server(
                                            Implementation(
                                                name = "mcp-kotlin test server",
                                                version = "0.1.0"
                                            ),
                                            ServerOptions(
                                                capabilities = ServerCapabilities(
                                                    prompts = ServerCapabilities.Prompts(listChanged = true),
                                                    resources = ServerCapabilities.Resources(subscribe = true, listChanged = true),
                                                    tools = ServerCapabilities.Tools(listChanged = true),
                                                )
                                            )
                                        )
                                                            
                                        // Add a tool
                                        server.addTool(
                                            name = "kotlin-sdk-tool",
                                            description = "A test tool",
                                            inputSchema = Tool.Input()
                                        ) { _ ->
                                            CallToolResult(
                                                content = listOf(TextContent("Hello, world!"))
                                            )
                                        }
                                                            
                                        return server
                                    }
                                """.trimIndent()
                            })
                        }
                    )
                }
            )
        },
        SectionEntry().apply {
            href = "section-2"
            title = "三、实战：业务API智能接入"
            contents = mutableListOf(
                ContentEntry().apply {
                    type = ContentType.P.value
                    classes = "mb-3"
                    value = "以下业务API智能接入方案由温州大佬提供，敬礼！( ` ・´)7"
                }
            )
            children = mutableListOf(
                SectionEntry().apply {
                    href = "section-2-1"
                    title = "3.1 三阶能力暴露方案"
                    contents = mutableListOf(
                        ContentEntry().apply {
                            type = ContentType.P.value
                            classes = "mb-3"
                            value = "1.接口目录服务"
                        },
                        ContentEntry().apply {
                            type = ContentType.PRE.value
                            classes = "bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto font-geist-mono mt-2 mb-3"
                            children.add(ContentEntry().apply {
                                type = ContentType.CODE.value
                                value = """
                                    // 1. 基础元工具：获取所有可用API列表
                                    server.addTool(
                                        name = "listAllApis",
                                        description = "列出所有可用的API接口",
                                        inputSchema = Tool.Input()
                                    ) {
                                        try {
                                            // 模拟数据
                                            val responseBuilder = StringBuilder("可用API列表(apiId-Name-Description):\n\n")
                                            .append("用户查询:\n").append("  • getUserInfo-获取用户信息-根据用户名称获取用户信息\n")
                                            .append("可使用 getApiDetails 工具获取特定API的详细信息,其中source表示来源")
                                                            
                                            CallToolResult(content = listOf(TextContent(responseBuilder.toString())))
                                        } catch (e: Exception) {
                                            CallToolResult(content = listOf(TextContent("错误: 获取API列表失败 - ${"\\$"}{e.message}")))
                                        }
                                    }
                                """.trimIndent()
                            })
                        },
                        ContentEntry().apply {
                            type = ContentType.P.value
                            classes = "mb-3"
                            value = "2.详情查询服务"
                        },
                        ContentEntry().apply {
                            type = ContentType.PRE.value
                            classes = "bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto font-geist-mono mt-2 mb-3"
                            children.add(ContentEntry().apply {
                                type = ContentType.CODE.value
                                value = """
                                    // 2. 基础元工具：获取特定API的详细信息
                                    server.addTool(
                                        name = "getApiDetails",
                                        description = "获取特定API的详细信息和使用方法",
                                        inputSchema = Tool.Input(
                                            properties = buildJsonObject {
                                            putJsonObject("apiId") {
                                                put("type", "string")
                                                put("description", "API的唯一标识符")
                                            }
                                        },
                                        required = listOf("apiId")
                                        )
                                    ) { request ->
                                        val apiId = request.arguments["apiId"]?.jsonPrimitive?.content
                                        ?: return@addTool CallToolResult(content = listOf(TextContent("错误: 缺少参数 apiId")))

                                        // 模拟数据
                                        val api = "{\"id\":\"getUserInfo\",\"name\":\"获取用户信息\",\"description\":\"根据用户名称获取用户信息\",\"category\":\"用户查询\",\"params\":[{\"name\":\"username\",\"type\":\"String\",\"description\":\"用户名称\",\"required\":true,\"defaultValue\":\"\",\"source\":\"\"}]}"

                                        CallToolResult(content = listOf(TextContent(api)))
                                    }
                                """.trimIndent()
                            })
                        },
                        ContentEntry().apply {
                            type = ContentType.P.value
                            classes = "mb-3"
                            value = "3.执行代理服务"
                        },
                        ContentEntry().apply {
                            type = ContentType.PRE.value
                            classes = "bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto font-geist-mono mt-2 mb-3"
                            children.add(ContentEntry().apply {
                                type = ContentType.CODE.value
                                value = """
                                    // 3. 基础元工具：执行指定API
                                    server.addTool(
                                        name = "executeApi",
                                        description = "执行指定的API,需要提供API的ID和参数",
                                        inputSchema = Tool.Input(
                                            properties = buildJsonObject {
                                                putJsonObject("apiId") {
                                                    put("type", "string")
                                                    put("description", "要执行的API的唯一标识符")
                                                }
                                                putJsonObject("params") {
                                                    put("type", "object")
                                                    put("description", "API的参数")
                                                }
                                            },
                                            required = listOf("apiId")
                                        )
                                    ) { request ->
                                        val apiId = request.arguments["apiId"]?.jsonPrimitive?.content
                                        val params = request.arguments["params"]?.let { jsonElement ->
                                            if (jsonElement is JsonPrimitive)
                                                jsonElement.jsonPrimitive.content
                                            else
                                                jsonElement.toString()
                                        } ?: "{}"
                                                            
                                        if (apiId == null) {
                                            return@addTool CallToolResult(content = listOf(TextContent("错误: 缺少参数 apiId")))
                                        }
                                        
                                        // 模拟调用接口响应数据
                                        val map = mutableMapOf("lenz" to "lenz是一名使用Kotlin的后端开发者")

                                        CallToolResult(content = listOf(TextContent(map[Gson().fromJson(params, Map::class.java).getOrDefault("username", "")])))
                                    }
                                """.trimIndent()
                            })
                        }
                    )
                },
                SectionEntry().apply {
                    href = "section-2-2"
                    title = "3.2 测试验证流程"
                    contents = mutableListOf(
                        ContentEntry().apply {
                            type = ContentType.P.value
                            classes = "mb-3"
                            value = "1.环境准备"
                        },
                        ContentEntry().apply {
                            type = ContentType.PRE.value
                            classes = "bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto font-geist-mono mt-2 mb-3"
                            value = "安装Cherry Studio（最新版）+ 有效API Key"
                        },
                        ContentEntry().apply {
                            type = ContentType.P.value
                            classes = "mb-3"
                            value = "2.服务配置"
                        },
                        ContentEntry().apply {
                            type = ContentType.PRE.value
                            classes = "bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto font-geist-mono mt-2 mb-3"
                            value = """
                                设置 → MCP服务器 → 新增：
                                - 类型：SSE
                                - 地址：http://localhost:3001/sse
                            """.trimIndent()
                        },
                        ContentEntry().apply {
                            type = ContentType.FIGURE.value
                            classes = "imageFigure cursor-pointer relative z-10 mb-3"
                            children = mutableListOf(
                                ContentEntry().apply {
                                    type = ContentType.DIV.value
                                    classes = "bg-gray-100 dark:bg-gray-800 aspect-video rounded-lg flex items-center justify-center text-gray-400"
                                    children = mutableListOf(
                                        ContentEntry().apply {
                                            type = ContentType.IMG.value
                                            classes = "w-full h-auto object-contain rounded-lg"
                                            value = "Image preview"
                                            href = "https://lenz.pics/image/20250417/1744904108579_cherry_studio_1.png"
                                        }
                                    )
                                },
                                ContentEntry().apply {
                                    type = ContentType.FIGCAPTION.value
                                    classes = "text-sm text-gray-500 dark:text-gray-400 text-center mt-2"
                                    value = "Figure 1: Cherry Studio配置MCP服务器示例"
                                }
                            )
                        },
                        ContentEntry().apply {
                            type = ContentType.P.value
                            classes = "mb-3"
                            value = "3.对话测试"
                        },
                        ContentEntry().apply {
                            type = ContentType.PRE.value
                            classes = "bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto font-geist-mono mt-2 mb-3"
                            value = """
                                用户：询问用户lenz的信息
                                → LLM自动选择list_apis工具
                                → 定位用户信息API
                                → 调用执行代理获取结果
                            """.trimIndent()
                        },
                        ContentEntry().apply {
                            type = ContentType.FIGURE.value
                            classes = "imageFigure cursor-pointer relative z-10 mb-3"
                            children = mutableListOf(
                                ContentEntry().apply {
                                    type = ContentType.DIV.value
                                    classes = "bg-gray-100 dark:bg-gray-800 aspect-video rounded-lg flex items-center justify-center text-gray-400"
                                    children = mutableListOf(
                                        ContentEntry().apply {
                                            type = ContentType.IMG.value
                                            classes = "w-full h-auto object-contain rounded-lg"
                                            value = "Image preview"
                                            href = "https://lenz.pics/image/20250417/1744905082809_cherry_studio_2.png"
                                        }
                                    )
                                },
                                ContentEntry().apply {
                                    type = ContentType.FIGCAPTION.value
                                    classes = "text-sm text-gray-500 dark:text-gray-400 text-center mt-2"
                                    value = "Figure 2: Cherry Studio对话测试示例"
                                }
                            )
                        }
                    )
                }
            )
        },
        SectionEntry().apply {
            href = "conclusion"
            title = "四、总结"
            contents = mutableListOf(
                ContentEntry().apply {
                    type = ContentType.P.value
                    classes = "mb-3"
                    value = "感谢您阅读本文！实现MCP服务器的核心逻辑并不复杂，但需要注意开发环境与依赖库版本的兼容性问题。我已将完整的业务API集成代码开源在GitHub仓库 kotlin-mcp-server，其中包含了可复用的配置方案和版本管理建议。"
                },
                ContentEntry().apply {
                    type = ContentType.P.value
                    classes = "mb-3"
                    value = "后续我将持续更新该系列文章，下一篇将重点讲解客户端实现的关键技术与实战演示。"
                }
            )
        }
    )
}