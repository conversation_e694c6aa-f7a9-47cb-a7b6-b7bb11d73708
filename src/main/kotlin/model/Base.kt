package org.example.model

import io.ktor.http.HttpStatusCode
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.ktor.server.routing.Route
import io.ktor.server.routing.delete
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.batchInsert
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.transactions.transaction
import kotlin.collections.forEach
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible

open class BaseEntity(private val table: Table) {
    var id: String = ""

    fun toInsertParams(): List<InsertPair<*>> {
        return this::class.memberProperties.mapNotNull { prop ->
            prop.isAccessible = true
            table.columns.find { it.name == camelToSnake(prop.name) }?.let { InsertPair(it, prop.getter.call(this)) }
        }
    }
}

fun camelToSnake(str: String): String {
    return str.replace(Regex("([a-z])([A-Z])"), "$1_$2")
        .lowercase()
}

data class InsertPair<T>(val column: Column<T>, val value: Any?)

interface CrudRepository<T, ID> {
    suspend fun create(entity: T): T
    suspend fun getById(id: ID): T?
    suspend fun getList(page: Int = 1, pageSize: Int = 10): List<T>
    suspend fun update(entity: T): T
    suspend fun delete(id: ID): Boolean
    suspend fun batchCreate(entities: List<T>): List<T>
}

abstract class BaseTableService<T : BaseEntity, ID, Table : org.jetbrains.exposed.sql.Table>(
    private val table: Table
) : CrudRepository<T, ID> {

    // 抽象方法，要求子类提供ID列
    protected abstract val idColumn: Column<ID>

    protected abstract fun ResultRow.toEntity(): T

    // 修改返回类型为具体的列类型映射

    override suspend fun getById(id: ID): T? = dbQuery {
        table.select { idColumn eq id }
            .singleOrNull()
            ?.toEntity()
    }

    override suspend fun getList(page: Int, pageSize: Int): List<T> = dbQuery {
        val offset = (page - 1) * pageSize
        table.selectAll()
            .limit(pageSize, offset.toLong())
            .map { it.toEntity() }
    }

    override suspend fun create(entity: T): T = dbQuery {
        table.insert { stmt ->
            entity.toInsertParams().forEach { pair ->
                (pair.column as Column<Any?>).let { col ->
                    stmt[col] = pair.value
                }
            }
        }
        entity
    }

    override suspend fun batchCreate(entities: List<T>): List<T> = dbQuery {
        table.batchInsert(entities) { entity ->
            entity.toInsertParams().forEach { pair ->
                (pair.column as Column<Any?>).let { col ->
                    this[col] = pair.value
                }
            }
        }
        entities
    }

    override suspend fun update(entity: T): T = dbQuery {
        // 需要具体表实现
        entity
    }

    override suspend fun delete(id: ID): Boolean = dbQuery {
        table.deleteWhere { idColumn eq id } > 0
    }

    suspend fun <T> dbQuery(block: () -> T): T =
        withContext(Dispatchers.IO) {
            transaction { block() }
        }
}

inline fun <reified T: Any, ID: Any> Route.crudRoutes(
    path: String,
    service: CrudRepository<T, ID>,
    noinline idParser: (String) -> ID
) {
    route("/$path") {
        // 创建
        post {
            val entity = call.receive<T>()
            call.respond(service.create(entity))
        }

        // 获取列表
        get {
            val page = call.request.queryParameters["page"]?.toIntOrNull() ?: 1
            val pageSize = call.request.queryParameters["pageSize"]?.toIntOrNull() ?: 10
            call.respond(service.getList(page, pageSize))
        }

        route("/{id}") {
            // 获取单个
            get {
                val id = idParser(call.parameters["id"]!!)
                service.getById(id)?.let { call.respond(it) }
                    ?: call.respond(HttpStatusCode.NotFound)
            }

            // 更新
            put {
                val id = idParser(call.parameters["id"]!!)
                val entity = call.receive<T>()
                call.respond(service.update(entity))
            }

            // 删除
            delete {
                val id = idParser(call.parameters["id"]!!)
                if (service.delete(id)) {
                    call.respond(HttpStatusCode.OK)
                } else {
                    call.respond(HttpStatusCode.NotFound)
                }
            }
        }
    }
}