let sectionIndex = 0;
let selectedBlock = null; // 当前选中的块

document.addEventListener("DOMContentLoaded", () => {
    lucide.createIcons();
    const sectionsContainer = document.getElementById("sectionsContainer");
    const blogContainer = document.getElementById("blogContainer");
    const dynamicButtonsContainer = document.getElementById("dynamicButtonsContainer");
    // const addSectionBtn = document.getElementById("addSectionBtn");

    // 点击选中 section-block
    blogContainer.addEventListener("click", (e) => {
        const selectableEl = e.target.closest(".selectable-block");
        if (selectableEl) {
            // 清除上一次选中样式
            if (selectedBlock) {
                selectedBlock.classList.remove("ring", "ring-blue-400", "bg-blue-50");
            }
            // 设置当前选中
            selectedBlock = selectableEl;
            selectableEl.classList.add("ring", "ring-blue-400", "bg-blue-50");

            // 清空按钮容器
            dynamicButtonsContainer.innerHTML = "";

            // 判断类型
            if (selectedBlock.classList.contains("child-section-block")) {
                dynamicButtonsContainer.innerHTML = `
                <button type="button" 
                        class="addContentBtn w-full flex items-center justify-center gap-2 text-gray-700 bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg px-4 py-2 transition">
                        <i data-lucide="pen" class="w-4 h-4"></i>
                        添加段落
                </button>
            `;
            } else if (selectedBlock.classList.contains("section-block")) {
                dynamicButtonsContainer.innerHTML = `
                    <button type="button" 
                            class="addChildrenSectionBtn w-full flex items-center gap-2 text-gray-700 bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg px-4 py-2 transition text-left">
                        <i data-lucide="list-plus" class="w-4 h-4"></i>
                        添加子章节
                    </button>
                    <button type="button" 
                            class="addContentBtn w-full flex items-center gap-2 text-gray-700 bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg px-4 py-2 transition text-left">
                        <i data-lucide="pen" class="w-4 h-4"></i>
                        添加段落
                    </button>
                `;
            } else if (selectedBlock.classList.contains("blogContainer")) {
                dynamicButtonsContainer.innerHTML = `
                    <button type="button" 
                        class="addSectionBtn w-full flex items-center justify-center gap-2 text-gray-700 bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg px-4 py-2 transition">
                        <i data-lucide="book-plus" class="w-4 h-4"></i>
                        添加章节
                    </button>
                `;
            } else {
                dynamicButtonsContainer.innerHTML = "";
            }

            // 重新渲染 Lucide 图标
            lucide.createIcons();
        }
    });

    addSectionBtn.addEventListener("click", () => {
        const sectionHtml = `
            <div class="selectable-block section-block cursor-pointer" data-section="${sectionIndex}">
                <label class="block text-sm font-medium text-gray-700">章节标题</label>
                <input type="text" name="sections[${sectionIndex}][title]" 
                       class="mt-1 block w-full border border-gray-300 rounded p-2" 
                       placeholder="章节标题">

                <div class="contentsContainer mt-4"></div>
                <div class="childrenSectionsContainer mt-4"></div>
            </div>
        `;
        sectionsContainer.insertAdjacentHTML("beforeend", sectionHtml);
        sectionIndex++;
    });

    // 添加章节按钮事件
    dynamicButtonsContainer.addEventListener("click", (e) => {
        if (e.target.classList.contains("addSectionBtn")) {
            const sectionHtml = `
                <div class="selectable-block section-block cursor-pointer" data-section="${sectionIndex}">
                    <label class="block text-sm font-medium text-gray-700">章节标题</label>
                    <input type="text" name="sections[${sectionIndex}][title]" 
                           class="mt-1 block w-full border border-gray-300 rounded p-2" 
                           placeholder="章节标题">
    
                    <div class="contentsContainer mt-4"></div>
                    <div class="childrenSectionsContainer mt-4"></div>
                </div>
            `;
            sectionsContainer.insertAdjacentHTML("beforeend", sectionHtml);
            sectionIndex++;
        }
    });

    // 添加子章节按钮事件
    dynamicButtonsContainer.addEventListener("click", (e) => {
        if (e.target.classList.contains("addChildrenSectionBtn")) {
            if (!selectedBlock || !selectedBlock.classList.contains("section-block")) {
                alert("请先选择一个章节！");
                return;
            }
            const sectionEl = selectedBlock;
            const sectionId = sectionEl.dataset.section;
            const childrenSectionsContainer = sectionEl.querySelector(".childrenSectionsContainer");
            const childrenSectionIndex = childrenSectionsContainer.children.length;

            const childrenSectionHtml = `
                <div class="selectable-block section-block child-section-block cursor-pointer" data-section="${sectionId}-${childrenSectionIndex}">
                    <label class="block text-sm font-medium text-gray-700">子章节标题</label>
                    <input type="text" name="sections[${sectionId}][children][${childrenSectionIndex}][title]" 
                           class="mt-1 block w-full border border-gray-300 rounded p-2" 
                           placeholder="子章节标题">
                    <div class="childrenSectionsContainer mt-4"></div>
                    <div class="contentsContainer mt-4"></div>
                </div>
            `;
            childrenSectionsContainer.insertAdjacentHTML("beforeend", childrenSectionHtml);
        }
    });

    // 添加段落按钮事件
    dynamicButtonsContainer.addEventListener("click", (e) => {
        if (e.target.classList.contains("addContentBtn")) {
            const sectionEl = selectedBlock;
            const sectionId = sectionEl.dataset.section;
            const contentsContainer = sectionEl.querySelector(".contentsContainer");
            const contentIndex = contentsContainer.children.length;

            const contentHtml = `
            <div class="selectable-block content-block">
                <label class="block text-sm font-medium text-gray-700">段落内容</label>
                <textarea name="sections[${sectionId}][contents][${contentIndex}][value]" 
                          rows="3" 
                          class="mt-1 block w-full border border-gray-300 rounded p-2"></textarea>
            </div>
        `;
            contentsContainer.insertAdjacentHTML("beforeend", contentHtml);
        }
    });
});
