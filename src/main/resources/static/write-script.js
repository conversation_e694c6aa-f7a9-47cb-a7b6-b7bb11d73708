let sectionIndex = 0;
let selectedBlock = null; // 当前选中的块

document.addEventListener("DOMContentLoaded", () => {
    lucide.createIcons();
    const sectionsContainer = document.getElementById("sectionsContainer");
    const blogContainer = document.getElementById("blogContainer");
    const dynamicButtonsContainer = document.getElementById("dynamicButtonsContainer");
    // const addSectionBtn = document.getElementById("addSectionBtn");

    // 点击选中 section-block
    blogContainer.addEventListener("click", (e) => {
        const selectableEl = e.target.closest(".selectable-block");
        if (selectableEl) {
            // 清除上一次选中样式
            if (selectedBlock) {
                selectedBlock.classList.remove("ring", "ring-blue-400", "bg-blue-50");
            }
            // 设置当前选中
            selectedBlock = selectableEl;
            selectableEl.classList.add("ring", "ring-blue-400", "bg-blue-50");

            // 清空按钮容器
            dynamicButtonsContainer.innerHTML = "";

            // 判断类型并生成对应按钮
            if (selectedBlock.classList.contains("child-section-block")) {
                dynamicButtonsContainer.innerHTML = `
                <button type="button"
                        class="addContentBtn dynamic-btn success w-full flex items-center justify-center gap-2 text-white">
                        <i data-lucide="pen" class="w-4 h-4"></i>
                        添加段落
                </button>
            `;
            } else if (selectedBlock.classList.contains("section-block")) {
                dynamicButtonsContainer.innerHTML = `
                    <button type="button"
                            class="addChildrenSectionBtn dynamic-btn primary w-full flex items-center justify-center gap-2 text-white mb-2">
                        <i data-lucide="list-plus" class="w-4 h-4"></i>
                        添加子章节
                    </button>
                    <button type="button"
                            class="addContentBtn dynamic-btn success w-full flex items-center justify-center gap-2 text-white">
                        <i data-lucide="pen" class="w-4 h-4"></i>
                        添加段落
                    </button>
                `;
            } else if (selectedBlock.classList.contains("blogContainer")) {
                dynamicButtonsContainer.innerHTML = `
                    <button type="button"
                        class="addSectionBtn dynamic-btn primary w-full flex items-center justify-center gap-2 text-white">
                        <i data-lucide="book-plus" class="w-4 h-4"></i>
                        添加章节
                    </button>
                `;
            } else {
                dynamicButtonsContainer.innerHTML = "";
            }

            // 重新渲染 Lucide 图标
            lucide.createIcons();
        }
    });

    addSectionBtn.addEventListener("click", () => {
        const sectionHtml = `
            <div class="selectable-block section-block cursor-pointer transition-all duration-200" data-section="${sectionIndex}">
                <div class="flex items-center gap-2 mb-3">
                    <i data-lucide="book-open" class="w-5 h-5 text-blue-500"></i>
                    <label class="block text-sm font-semibold text-gray-700">章节标题</label>
                </div>
                <input type="text" name="sections[${sectionIndex}][title]"
                       class="block w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-400 focus:border-blue-400 outline-none transition-all duration-200"
                       placeholder="输入章节标题...">

                <div class="contentsContainer mt-6"></div>
                <div class="childrenSectionsContainer mt-6"></div>
            </div>
        `;
        sectionsContainer.insertAdjacentHTML("beforeend", sectionHtml);
        sectionIndex++;
        lucide.createIcons();
    });

    // 添加章节按钮事件
    dynamicButtonsContainer.addEventListener("click", (e) => {
        if (e.target.classList.contains("addSectionBtn")) {
            const sectionHtml = `
                <div class="selectable-block section-block cursor-pointer transition-all duration-200" data-section="${sectionIndex}">
                    <div class="flex items-center gap-2 mb-3">
                        <i data-lucide="book-open" class="w-5 h-5 text-blue-500"></i>
                        <label class="block text-sm font-semibold text-gray-700">章节标题</label>
                    </div>
                    <input type="text" name="sections[${sectionIndex}][title]"
                           class="block w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-400 focus:border-blue-400 outline-none transition-all duration-200"
                           placeholder="输入章节标题...">

                    <div class="contentsContainer mt-6"></div>
                    <div class="childrenSectionsContainer mt-6"></div>
                </div>
            `;
            sectionsContainer.insertAdjacentHTML("beforeend", sectionHtml);
            sectionIndex++;
            lucide.createIcons();
        }
    });

    // 添加子章节按钮事件
    dynamicButtonsContainer.addEventListener("click", (e) => {
        if (e.target.classList.contains("addChildrenSectionBtn")) {
            if (!selectedBlock || !selectedBlock.classList.contains("section-block")) {
                alert("请先选择一个章节！");
                return;
            }
            const sectionEl = selectedBlock;
            const sectionId = sectionEl.dataset.section;
            const childrenSectionsContainer = sectionEl.querySelector(".childrenSectionsContainer");
            const childrenSectionIndex = childrenSectionsContainer.children.length;

            const childrenSectionHtml = `
                <div class="selectable-block section-block child-section-block cursor-pointer transition-all duration-200 ml-4 border-l-4 border-blue-200" data-section="${sectionId}-${childrenSectionIndex}">
                    <div class="flex items-center gap-2 mb-3">
                        <i data-lucide="bookmark" class="w-4 h-4 text-green-500"></i>
                        <label class="block text-sm font-semibold text-gray-700">子章节标题</label>
                    </div>
                    <input type="text" name="sections[${sectionId}][children][${childrenSectionIndex}][title]"
                           class="block w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-green-400 focus:border-green-400 outline-none transition-all duration-200"
                           placeholder="输入子章节标题...">
                    <div class="childrenSectionsContainer mt-6"></div>
                    <div class="contentsContainer mt-6"></div>
                </div>
            `;
            childrenSectionsContainer.insertAdjacentHTML("beforeend", childrenSectionHtml);
            lucide.createIcons();
        }
    });

    // 添加段落按钮事件
    dynamicButtonsContainer.addEventListener("click", (e) => {
        if (e.target.classList.contains("addContentBtn")) {
            const sectionEl = selectedBlock;
            const sectionId = sectionEl.dataset.section;
            const contentsContainer = sectionEl.querySelector(".contentsContainer");
            const contentIndex = contentsContainer.children.length;

            const contentHtml = `
            <div class="selectable-block content-block transition-all duration-200">
                <div class="flex items-center gap-2 mb-3">
                    <i data-lucide="file-text" class="w-4 h-4 text-purple-500"></i>
                    <label class="block text-sm font-semibold text-gray-700">段落内容</label>
                </div>
                <textarea name="sections[${sectionId}][contents][${contentIndex}][value]"
                          rows="4"
                          class="block w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-purple-400 focus:border-purple-400 outline-none transition-all duration-200 resize-vertical"
                          placeholder="输入段落内容..."></textarea>
            </div>
        `;
            contentsContainer.insertAdjacentHTML("beforeend", contentHtml);
            lucide.createIcons();
        }
    });
});
