let sectionIndex = 0;

document.addEventListener("DOMContentLoaded", () => {
    const sectionsContainer = document.getElementById("sectionsContainer");
    const addSectionBtn = document.getElementById("addSectionBtn");

    addSectionBtn.addEventListener("click", () => {
        const sectionHtml = `
            <div class="section-block" data-section="${sectionIndex}">
                <label class="block text-sm font-medium text-gray-700">章节标题</label>
                <input type="text" name="sections[${sectionIndex}][title]" 
                       class="mt-1 block w-full border border-gray-300 rounded p-2" 
                       placeholder="章节标题">

                <div class="contentsContainer mt-4"></div>
                <div class="childrenSectionsContainer mt-4"></div>

                <button type="button" 
                        class="addChildrenSectionBtn bg-yellow-500 text-white px-2 py-1 rounded mt-2">
                        添加子章节
                </button>
                <button type="button" 
                        class="addContentBtn bg-yellow-500 text-white px-2 py-1 rounded mt-2">
                        添加段落
                </button>
            </div>
        `;
        sectionsContainer.insertAdjacentHTML("beforeend", sectionHtml);
        sectionIndex++;
    });

    sectionsContainer.addEventListener("click", (e) => {
        if (e.target.classList.contains("addContentBtn")) {
            const sectionEl = e.target.closest(".section-block");
            const sectionId = sectionEl.dataset.section;
            const contentsContainer = sectionEl.querySelector(".contentsContainer");
            const contentIndex = contentsContainer.children.length;
            const contentHtml = `
                <div class="content-block">
                    <label class="block text-sm font-medium text-gray-700">段落内容</label>
                    <textarea name="sections[${sectionId}][contents][${contentIndex}][value]" 
                              rows="3" 
                              class="mt-1 block w-full border border-gray-300 rounded p-2">
                    </textarea>
                </div>
            `;
            contentsContainer.insertAdjacentHTML("beforeend", contentHtml);
        }
    });

    sectionsContainer.addEventListener("click", (e) => {
        if (e.target.classList.contains("addChildrenSectionBtn")) {
            const sectionEl = e.target.closest(".section-block");
            const sectionId = sectionEl.dataset.section;
            const childrenSectionsContainer = sectionEl.querySelector(".childrenSectionsContainer");
            const childrenSectionIndex = childrenSectionsContainer.children.length;
            const childrenSectionHtml = `
                <div class="section-block" data-section="${sectionId}-${childrenSectionIndex}">
                    <label class="block text-sm font-medium text-gray-700">子章节标题</label>
                    <input type="text" name="sections[${sectionId}][children][${childrenSectionIndex}][title]" 
                           class="mt-1 block w-full border border-gray-300 rounded p-2" 
                           placeholder="子章节标题">
                           
                    <div class="contentsContainer mt-4"></div>
                    
                    <button type="button" 
                            class="addContentBtn bg-yellow-500 text-white px-2 py-1 rounded mt-2">
                            添加段落
                    </button>
                </div>
            `;
            childrenSectionsContainer.insertAdjacentHTML("beforeend", childrenSectionHtml);
        }
    });
});
