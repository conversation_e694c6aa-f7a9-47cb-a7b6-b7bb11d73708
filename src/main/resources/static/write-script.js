let sectionIndex = 0;
let selectedBlock = null; // 当前选中的块

document.addEventListener("DOMContentLoaded", () => {
    lucide.createIcons();
    const sectionsContainer = document.getElementById("sectionsContainer");
    const blogContainer = document.getElementById("blogContainer");
    const dynamicButtonsContainer = document.getElementById("dynamicButtonsContainer");

    // 点击选中 section-block
    blogContainer.addEventListener("click", (e) => {
        const selectableEl = e.target.closest(".selectable-block");
        if (selectableEl) {
            // 清除上一次选中样式
            if (selectedBlock) {
                selectedBlock.classList.remove("ring", "ring-blue-400", "bg-blue-50");
            }
            // 设置当前选中
            selectedBlock = selectableEl;
            selectableEl.classList.add("ring", "ring-blue-400", "bg-blue-50");

            // 清空按钮容器
            dynamicButtonsContainer.innerHTML = "";

            // 判断类型并生成对应按钮
            if (selectedBlock.classList.contains("child-section-block")) {
                // 子章节只能添加段落
                dynamicButtonsContainer.innerHTML = `
                <button type="button"
                        class="addContentBtn icon-btn success group relative">
                        <i data-lucide="pen" class="w-5 h-5"></i>
                        <span class="tooltip">添加段落</span>
                </button>
            `;
            } else if (selectedBlock.classList.contains("section-block") && !selectedBlock.classList.contains("child-section-block")) {
                // 主章节可以添加子章节和段落
                dynamicButtonsContainer.innerHTML = `
                    <button type="button"
                            class="addChildrenSectionBtn icon-btn primary group relative">
                        <i data-lucide="list-plus" class="w-5 h-5"></i>
                        <span class="tooltip">添加子章节</span>
                    </button>
                    <button type="button"
                            class="addContentBtn icon-btn success group relative">
                        <i data-lucide="pen" class="w-5 h-5"></i>
                        <span class="tooltip">添加段落</span>
                    </button>
                `;
            } else if (selectedBlock.classList.contains("blogContainer")) {
                // 博客容器只能添加章节
                dynamicButtonsContainer.innerHTML = `
                    <button type="button"
                        class="addSectionBtn icon-btn primary group relative">
                        <i data-lucide="book-plus" class="w-5 h-5"></i>
                        <span class="tooltip">添加章节</span>
                    </button>
                `;
            } else {
                // 上传图片文件按钮
                dynamicButtonsContainer.innerHTML = `
                    <button type="button"
                        class="addSectionBtn icon-btn primary group relative">
                        <i data-lucide="book-plus" class="w-5 h-5"></i>
                        <span class="tooltip">上传图片</span>
                    </button>
                `;
            }

            // 重新渲染 Lucide 图标
            lucide.createIcons();
        }
    });

    // 添加章节按钮事件
    dynamicButtonsContainer.addEventListener("click", (e) => {
        const button = e.target.closest(".addSectionBtn");
        if (button) {
            const sectionHtml = `
                <div class="selectable-block section-block cursor-pointer transition-all duration-200" data-section="${sectionIndex}">
                    <div class="flex items-center gap-2 mb-3">
                        <i data-lucide="book-open" class="w-5 h-5 text-blue-500"></i>
                        <label class="block text-sm font-semibold text-gray-700">章节标题</label>
                    </div>
                    <input type="text" name="sections[${sectionIndex}][title]"
                           class="block w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-400 focus:border-blue-400 outline-none transition-all duration-200"
                           placeholder="输入章节标题...">
                    <div class="contentsContainer mt-6"></div>
                    <div class="childrenSectionsContainer mt-6"></div>
                </div>
            `;
            sectionsContainer.insertAdjacentHTML("beforeend", sectionHtml);
            sectionIndex++;
            lucide.createIcons();
        }
    });

    // 添加子章节按钮事件
    dynamicButtonsContainer.addEventListener("click", (e) => {
        const button = e.target.closest(".addChildrenSectionBtn");
        if (button) {
            if (!selectedBlock || !selectedBlock.classList.contains("section-block") || selectedBlock.classList.contains("child-section-block")) {
                alert("请先选择一个主章节（不是子章节）！");
                return;
            }

            const sectionEl = selectedBlock;
            const sectionId = sectionEl.dataset.section;
            const childrenSectionsContainer = sectionEl.querySelector(".childrenSectionsContainer");

            if (!childrenSectionsContainer) {
                alert("找不到子章节容器，请确保选择了正确的章节！");
                return;
            }

            const childrenSectionIndex = childrenSectionsContainer.children.length;

            const childrenSectionHtml = `
                <div class="selectable-block section-block child-section-block cursor-pointer transition-all duration-200 ml-4 border-l-4 border-blue-200" data-section="${sectionId}-${childrenSectionIndex}">
                    <div class="flex items-center gap-2 mb-3">
                        <i data-lucide="bookmark" class="w-4 h-4 text-green-500"></i>
                        <label class="block text-sm font-semibold text-gray-700">子章节标题</label>
                    </div>
                    <input type="text" name="sections[${sectionId}][children][${childrenSectionIndex}][title]"
                           class="block w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-green-400 focus:border-green-400 outline-none transition-all duration-200"
                           placeholder="输入子章节标题...">
                    <div class="childrenSectionsContainer mt-6"></div>
                    <div class="contentsContainer mt-6"></div>
                </div>
            `;
            childrenSectionsContainer.insertAdjacentHTML("beforeend", childrenSectionHtml);
            lucide.createIcons();
        }
    });

    // 添加段落按钮事件
    dynamicButtonsContainer.addEventListener("click", (e) => {
        const button = e.target.closest(".addContentBtn");
        if (button) {
            const sectionEl = selectedBlock;
            const sectionId = sectionEl.dataset.section;
            const contentsContainer = sectionEl.querySelector(".contentsContainer");
            const contentIndex = contentsContainer.children.length;

            const contentHtml = `
                <div class="selectable-block content-block transition-all duration-200">
                    <div class="flex items-center gap-2 mb-3">
                        <i data-lucide="file-text" class="w-4 h-4 text-purple-500"></i>
                        <label class="block text-sm font-semibold text-gray-700">段落内容</label>
                    </div>
                    <textarea name="sections[${sectionId}][contents][${contentIndex}][value]"
                              rows="4"
                              class="block w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-purple-400 focus:border-purple-400 outline-none transition-all duration-200 resize-vertical"
                              placeholder="输入段落内容..."></textarea>
                    <div class="uploadFileContainer mt-6"></div>
                </div>
            `;
            contentsContainer.insertAdjacentHTML("beforeend", contentHtml);
            lucide.createIcons();
        }
    });

    // 预览按钮事件
    document.addEventListener("click", (e) => {
        const previewBtn = e.target.closest("#previewBtn");
        if (previewBtn) {
            const form = document.getElementById("blogForm");
            const formData = new FormData(form);

            // 简单的预览功能 - 显示表单数据
            let previewContent = "=== 文章预览 ===\n\n";
            previewContent += `标题: ${formData.get('title') || '未设置'}\n`;
            previewContent += `标签: ${formData.get('tags') || '无标签'}\n\n`;

            // 获取所有章节
            let sectionIndex = 0;
            while (formData.get(`sections[${sectionIndex}][title]`)) {
                previewContent += `章节 ${sectionIndex + 1}: ${formData.get(`sections[${sectionIndex}][title]`)}\n`;

                // 获取章节内容
                let contentIndex = 0;
                while (formData.get(`sections[${sectionIndex}][contents][${contentIndex}][value]`)) {
                    const content = formData.get(`sections[${sectionIndex}][contents][${contentIndex}][value]`);
                    if (content.trim()) {
                        previewContent += `  内容: ${content.substring(0, 100)}${content.length > 100 ? '...' : ''}\n`;
                    }
                    contentIndex++;
                }
                previewContent += "\n";
                sectionIndex++;
            }

            alert(previewContent);
        }
    });

    // 清空所有按钮事件
    document.addEventListener("click", (e) => {
        const clearBtn = e.target.closest("#clearAllBtn");
        if (clearBtn) {
            if (confirm("确定要清空所有内容吗？此操作不可撤销！")) {
                // 清空表单
                document.getElementById("blogForm").reset();
                // 清空动态生成的章节
                document.getElementById("sectionsContainer").innerHTML = "";
                // 重置选中状态
                if (selectedBlock) {
                    selectedBlock.classList.remove("ring", "ring-blue-400", "bg-blue-50");
                    selectedBlock = null;
                }
                // 重置动态按钮
                dynamicButtonsContainer.innerHTML = `
                    <button type="button"
                        class="addSectionBtn icon-btn primary group relative">
                        <i data-lucide="book-plus" class="w-5 h-5"></i>
                        <span class="tooltip">添加章节</span>
                    </button>
                `;
                lucide.createIcons();
                sectionIndex = 0;
            }
        }
    });
});
