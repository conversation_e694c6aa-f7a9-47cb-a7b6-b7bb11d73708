document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    const codeBlocks = document.querySelectorAll('pre code');
    const showHideBtn = document.querySelector('.show-hide-btn');
    const menuContainer = document.querySelector('.menu-container');
  
    codeBlocks.forEach(block => {
      const container = block.parentElement;
	  container.classList.add('code-container'); // 添加容器类
	  
	  const copyButton = document.createElement('button');
	  copyButton.className = 'copy-button';
	  copyButton.innerHTML = '<span class="icon-clipboard"></span> Copy';
	  container.appendChild(copyButton);

      
      copyButton.addEventListener('click', () => {
        // Get the text content of the code block
        const code = block.textContent;
        
        // Copy to clipboard
        navigator.clipboard.writeText(code)
          .then(() => {
            copyButton.innerHTML = '<span class="icon-check"></span> Copied!';
            setTimeout(() => {
              copyButton.innerHTML = '<span class="icon-clipboard"></span> Copy';
            }, 2000);
          })
          .catch(err => {
            console.error('Failed to copy: ', err);
            copyButton.innerHTML = '<span class="icon-alert-circle"></span> Error!';
            setTimeout(() => {
              copyButton.innerHTML = '<span class="icon-clipboard"></span> Copy';
            }, 2000);
          });
      });
    });

    const headings = document.querySelectorAll("article section, article div[id]");
    const tocLinks = document.querySelectorAll('.toc a');
    
    const tocMap = {};
    tocLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && href.startsWith('#')) {
            const id = href.slice(1);
            tocMap[id] = link;
        }
    });
    
    function updateActiveHeading() {
        let inView = [];
        let aboveView = [];
    
        headings.forEach(heading => {
            const rect = heading.getBoundingClientRect();
            if (rect.top >= 0 && rect.top < window.innerHeight) {
                inView.push({ heading, top: rect.top });
            } else if (rect.top < 0) {
                aboveView.push({ heading, top: rect.top });
            }
        });
    
        let targetHeading = null;
    
        if (inView.length > 0) {
            // 选择最接近顶部的可视 heading
            inView.sort((a, b) => a.top - b.top);
            targetHeading = inView[0].heading;
        } else if (aboveView.length > 0) {
            // 没有可视 heading，选择最后一个在视口上方的
            aboveView.sort((a, b) => b.top - a.top);
            targetHeading = aboveView[0].heading;
        }
    
        if (targetHeading) {
            const id = targetHeading.getAttribute('id');
            tocLinks.forEach(link => link.classList.remove('active'));
            if (tocMap[id]) {
                tocMap[id].classList.add('active');
            }
        }
    }
    
    // 初次渲染立即触发一次
    window.addEventListener('load', updateActiveHeading);
    // 滚动时实时更新
    window.addEventListener('scroll', updateActiveHeading, { passive: true });
    // resize 时可能也要更新
    window.addEventListener('resize', updateActiveHeading);
    
    tocLinks.forEach(link => {
        link.addEventListener('click', function (e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
    
            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 20,
                    behavior: 'smooth'
                });
    
                history.pushState(null, null, targetId);
            }
        });
    });
    
    // const figure = document.getElementById('imageFigure');
    const images = document.querySelectorAll('.imageFigure');
    const overlay = document.getElementById('overlay');

    let modalImage = null;

    for (let i = 0; i < images.length; i++) {
        const figure = images[i];
        figure.addEventListener('click', () => {
            // 获取原图地址
            const imgSrc = document.getElementById(`previewImg-${i}`).src;

            // 创建居中放大的图片元素
            modalImage = document.createElement('img');
            modalImage.src = imgSrc;
            modalImage.alt = 'Enlarged image';
            modalImage.className = `
                fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2
                max-w-5xl max-h-[90vh] z-50 rounded-xl transition-transform duration-300
                opacity-0 scale-90
            `;

            document.body.appendChild(modalImage);
            overlay.classList.remove('hidden');

            // 触发过渡动画（下一帧）
            requestAnimationFrame(() => {
                modalImage.classList.remove('opacity-0', 'scale-90');
                modalImage.classList.add('opacity-100', 'scale-100');
            });
        });
    }
    // images.forEach(figure => {
        
    // });
    showHideBtn.addEventListener('click', () => {
        document.startViewTransition(() => {
            menuContainer.classList.toggle('expanded');
        });
    });
});

