/* Custom styles for blog template */
:root {
  --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  --font-mono: "Geist Mono", ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

body {
  font-family: var(--font-sans);
  line-height: 1.6;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.font-geist-mono {
  font-family: var(--font-mono);
}

/* Table of Contents styles */
.toc li {
  transition: all 0.2s ease;
}

.toc li a {
  display: block;
  padding: 0.25rem 0;
  text-decoration: none;
  position: relative;
}

.toc li a:hover {
  padding-left: 0.25rem;
}

.toc li a.active {
  font-weight: 600;
  color: #3b82f6; /* blue-500 */
}

.toc li a.active::before {
  content: "";
  position: absolute;
  left: -0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 0.25rem;
  height: 0.25rem;
  border-radius: 50%;
  background-color: #3b82f6; /* blue-500 */
}

/* Article content styles */
article h2, article h3 {
  scroll-margin-top: 2rem;
}

article a {
  color: #3b82f6; /* blue-500 */
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s ease;
}

article a:hover {
  border-color: #3b82f6; /* blue-500 */
}

article pre {
  font-family: var(--font-mono);
  font-size: 0.875rem;
}

article img {
  border-radius: 0.5rem;
}

/* Comment form styles */
input:focus, textarea:focus {
  outline: none;
  border-color: #3b82f6; /* blue-500 */
}

/* Custom transitions */
.transition {
  transition: all 0.2s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .toc {
    display: flex;
    overflow-x: auto;
    white-space: nowrap;
    padding-bottom: 1rem;
  }
  
  .toc ul {
    display: flex;
  }
  
  .toc li {
    margin-right: 1rem;
  }
  
  .toc li.ml-4 {
    margin-left: 0;
    margin-right: 1rem;
  }
}


.code-container {
  position: relative; /* 为绝对定位的按钮创建参照 */
}

.copy-button {
  position: absolute;
  top: 5px;
  right: 5px;
  padding: 3px 8px;
  font-size: 12px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-button:hover {
  background-color: #e9e9e9;
}

/* 如果你使用图标字体，可以添加这些样式 */
.icon-clipboard:before {
  content: "📋";
  margin-right: 4px;
}

.icon-check:before {
  content: "✓";
  margin-right: 4px;
}

.icon-alert-circle:before {
  content: "⚠️";
  margin-right: 4px;
}

.message-link {
  color: #0066cc; /* 链接颜色（蓝色） */
  text-decoration: none; /* 可选：去掉下划线 */
}

.message-link:hover {
  text-decoration: underline; /* 悬停时显示下划线 */
  color: #73eec5; /* 悬停时的颜色 */
}

.menu-container {
}
