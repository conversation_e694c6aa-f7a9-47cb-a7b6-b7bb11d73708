/* Custom styles for blog template */
:root {
  --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  --font-mono: "Geist Mono", ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

body {
  font-family: var(--font-sans);
  line-height: 1.6;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.font-geist-mono {
  font-family: var(--font-mono);
}

/* Table of Contents styles */
.toc li {
  transition: all 0.2s ease;
}

.toc li a {
  display: block;
  padding: 0.25rem 0;
  text-decoration: none;
  position: relative;
}

.toc li a:hover {
  padding-left: 0.25rem;
}

.toc li a.active {
  font-weight: 600;
  color: #3b82f6; /* blue-500 */
}

.toc li a.active::before {
  content: "";
  position: absolute;
  left: -0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 0.25rem;
  height: 0.25rem;
  border-radius: 50%;
  background-color: #3b82f6; /* blue-500 */
}

/* Article content styles */
article h2, article h3 {
  scroll-margin-top: 2rem;
}

article a {
  color: #3b82f6; /* blue-500 */
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s ease;
}

article a:hover {
  border-color: #3b82f6; /* blue-500 */
}

article pre {
  font-family: var(--font-mono);
  font-size: 0.875rem;
}

article img {
  border-radius: 0.5rem;
}

/* Comment form styles */
input:focus, textarea:focus {
  outline: none;
  border-color: #3b82f6; /* blue-500 */
}

/* Custom transitions */
.transition {
  transition: all 0.2s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .toc {
    display: flex;
    overflow-x: auto;
    white-space: nowrap;
    padding-bottom: 1rem;
  }
  
  .toc ul {
    display: flex;
  }
  
  .toc li {
    margin-right: 1rem;
  }
  
  .toc li.ml-4 {
    margin-left: 0;
    margin-right: 1rem;
  }
}


.code-container {
  position: relative; /* 为绝对定位的按钮创建参照 */
}

.copy-button {
  position: absolute;
  top: 5px;
  right: 5px;
  padding: 3px 8px;
  font-size: 12px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-button:hover {
  background-color: #e9e9e9;
}

/* 如果你使用图标字体，可以添加这些样式 */
.icon-clipboard:before {
  content: "📋";
  margin-right: 4px;
}

.icon-check:before {
  content: "✓";
  margin-right: 4px;
}

.icon-alert-circle:before {
  content: "⚠️";
  margin-right: 4px;
}

.message-link {
  color: #0066cc; /* 链接颜色（蓝色） */
  text-decoration: none; /* 可选：去掉下划线 */
}

.message-link:hover {
  text-decoration: underline; /* 悬停时显示下划线 */
  color: #73eec5; /* 悬停时的颜色 */
}

.menu-container {
  background: white;
  border-radius: 24px;
  padding: 24px;
  width: 100%; /* 从固定 400px 改为继承父容器宽度 */
  box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 6px -1px, rgba(0, 0, 0, 0.1) 0px 2px 4px -2px;
  view-transition-name: menu-container;

  .menu-list-item:nth-child(1) {
    margin-bottom: -21.5%;
  }

  .menu-list-item:nth-child(2) {
    margin-bottom: -21.5%;
    transform: scale(0.95);
  }

  .menu-list-item:nth-child(3) {
    transform: scale(0.9);
  }

  .hide {
    display: none;
  }

  &.expanded {
    .menu-list-item:nth-child(1),
    .menu-list-item:nth-child(2) {
      margin-bottom: 0;
      transform: scale(1);
    }

    .menu-list-item:nth-child(3) {
      transform: scale(1);
    }

    .menu-show {
      display: none;
    }

    .menu-hide {
      display: block;
    }

    .show-hide-btn svg {
      transform: rotate(180deg);
      transition: transform 0.6s;
    }
  }
}

.menu-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  view-transition-name: list;
}

.menu-list-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: white;
  border-radius: 16px;
  border: 1px solid #e4e4e7;
  cursor: pointer;
  box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0.1) 0px 4px 6px -1px, rgba(0, 0, 0, 0.1) 0px 2px 4px -2px;
}

.menu-list-item:nth-child(1) {
  view-transition-name: menu-list-item-1;
  z-index: 2;
}

.menu-list-item:nth-child(2) {
  view-transition-name: menu-list-item-2;
  z-index: 1;
}

.menu-list-item:nth-child(3) {
  view-transition-name: menu-list-item-3;
  z-index: 0;
}

.menu-show,
.menu-hide {
  width: fit-content;
  view-transition-name: button-label;
}

.show-hide-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  margin: 8px auto 0 auto;
  width: 140px;
  background: none;
  color: #71717a;
  border: 1px solid #e4e4e7;
  border-radius: 24px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  view-transition-name: show-hide-btn;

  span {
    display: inline-block;
    text-align: center;
    width: fit-content;
  }

  svg {
    transition: transform 0.3s;
    view-transition-name: show-hide-btn-icon;
  }
}

@keyframes fade-out {
  0% {
    opacity: 1;
  }
  10% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

::view-transition-group(*) {
  animation-duration: 0.6s;
  animation-timing-function: linear(0, 0.402 7.4%, 0.711 15.3%, 0.929 23.7%, 1.008 28.2%, 1.067 33%, 1.099 36.9%, 1.12 41%, 1.13 45.4%, 1.13 50.1%, 1.111 58.5%, 1.019 83.2%, 1.004 91.3%, 1);
}

::view-transition-old(*),
::view-transition-new(*) {
  height: 100%;
}

::view-transition-old(button-label),
::view-transition-new(button-label) {
  width: fit-content;
}

::view-transition-old(button-label) {
  animation-name: fade-out;
  animation-duration: 0.6s;
}
